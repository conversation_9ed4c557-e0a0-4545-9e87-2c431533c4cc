package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import com.mc.common.validation.constraints.StringFormat;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.utils.IpUtil;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * .
 */
public class RedundancyBean {
  private SystemConfigData systemConfigData;

  private Boolean isRedundancy;
  private Boolean isSynchronize;
  private Boolean isEchoOnly;
  private String masterIp;

  private Boolean isDefaultBackup;

  private String virtualIp;

  private int virtualRouteId;

  public RedundancyBean(SystemConfigData systemConfigData) {
    this.systemConfigData = systemConfigData;
  }

  public Boolean getIsRedundancy() {
    isRedundancy = systemConfigData.getSystemData().isRedundancy();
    return isRedundancy;
  }

  public void setIsRedundancy(Boolean enabled) {
    systemConfigData.getSystemData().setRedundancy(enabled);
  }

  public Boolean getIsSynchronize() {
    isSynchronize = systemConfigData.getSystemData().isSynchronize();
    return isSynchronize;
  }

  public void setIsSynchronize(Boolean enabled) {
    systemConfigData.getSystemData().setSynchronize(enabled);
  }

  public Boolean getIsEchoOnly() {
    isEchoOnly = systemConfigData.getSystemData().isEchoOnly();
    return isEchoOnly;
  }

  public void setIsEchoOnly(Boolean enabled) {
    systemConfigData.getSystemData().setEchoOnly(enabled);
  }

  /** 获取主用主机IP. */
  @StringFormat(format = "[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}")
  public String getMasterIp() {
    masterIp = IpUtil.getAddressString(systemConfigData.getSystemData().getMasterIp());
    return masterIp;
  }

  /** 设置主用主机IP. */
  public void setMasterIp(String ip) {
    systemConfigData.getSystemData().setMasterIp(IpUtil.getAddressByte(ip));
  }

  public Boolean getIsDefaultBackup() {
    isDefaultBackup = systemConfigData.getSystemData().isDefaultBackup();
    return isDefaultBackup;
  }

  public void setIsDefaultBackup(Boolean enable) {
    systemConfigData.getSystemData().setDefaultBackup(enable);
  }

  @StringFormat(format = "[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}")
  public String getVirtualIp() {
    virtualIp = IpUtil.getAddressString(systemConfigData.getNetworkDataPreset2().getAddress());
    return virtualIp;
  }

  public void setVirtualIp(String ip) {
    systemConfigData.getNetworkDataPreset2().setAddress(IpUtil.getAddressByte(ip));
  }

  @Max(255)
  @Min(1)
  public int getVirtualRouteId() {
    virtualRouteId = systemConfigData.getNetworkDataPreset2().getNetworkBits();
    return virtualRouteId;
  }

  /**
   * 设置虚拟路由ID.
   */
  public void setVirtualRouteId(int virtualRouteId) {
    if (systemConfigData.getNetworkDataPreset4().getNetworkBits() != virtualRouteId) {
      systemConfigData.getNetworkDataPreset2().setNetworkBits(virtualRouteId);
    }
  }
}
