package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarVideoWallFuncManager;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarData2VisualDataModel;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.util.ArrayList;
import java.util.List;
import javafx.beans.property.ReadOnlyIntegerWrapper;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.scene.Node;
import javafx.scene.control.AlertEx;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.MenuItem;
import javafx.scene.control.SelectionMode;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.CheckBoxTableCell;
import javafx.scene.input.KeyCode;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.stage.Window;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * OfflineManagerView.
 */
@Slf4j
public class OfflineManagerView extends TableView<CaesarVideoWallFunc> {

  private final ObservableList<CaesarVideoWallFunc> videoWallList =
      FXCollections.observableArrayList();

  @Setter
  private CaesarData2VisualDataModel dataConverter;

  private final WeakAdapter weakAdapter = new WeakAdapter();

  /**
   * 离线设备管理.
   */
  public OfflineManagerView() {

    getSelectionModel().setSelectionMode(SelectionMode.SINGLE);

    // 右键菜单
    ContextMenu contextMenu = new ContextMenu();
    MenuItem deleteItem = new MenuItem(CaesarI18nCommonResource.getString("offline_manager.delete_selected"));
    deleteItem.setOnAction(e -> delete(getSelectionModel().getSelectedItem()));
    contextMenu.getItems().add(deleteItem);
    deleteItem.disableProperty().bind(getSelectionModel().selectedItemProperty().isNull());

    MenuItem multiDeleteItem = new MenuItem(CaesarI18nCommonResource.getString("offline_manager.delete_checked"));
    multiDeleteItem.setOnAction(e -> delete());
    contextMenu.getItems().add(multiDeleteItem);
    setContextMenu(contextMenu);

    this.addEventFilter(MouseEvent.MOUSE_PRESSED, event -> {
      if (event.getButton() == MouseButton.PRIMARY) {
        Node target = (Node) event.getTarget();
        boolean isClickOnRow = target.getParent() instanceof TableRow;
        if (!isClickOnRow) {
          getSelectionModel().clearSelection();
        }
      }
    });

    setRowFactory(tv -> {
      TableRow<CaesarVideoWallFunc> row = new TableRow<>();
      // 右键点击时选中该行
      row.setOnMouseClicked(e -> {
        if (e.getButton() == MouseButton.SECONDARY && !row.isEmpty()) {
          getSelectionModel().select(row.getIndex());
        }
      });
      return row;
    });

    // 添加键盘删除勾选项支持（按 DELETE 键删除）
    setOnKeyPressed(e -> {
      if (e.getCode() == KeyCode.DELETE) {
        delete();
      }
    });
    TableColumn<CaesarVideoWallFunc, Boolean> selectedColumn = new TableColumn<>();
    CheckBox selectAllCheckBox = new CheckBox();
    selectAllCheckBox.selectedProperty().addListener(weakAdapter.wrap(
        (observable, oldValue, newValue) -> {
          if (newValue != null) {
            for (CaesarVideoWallFunc item : this.getItems()) {
              item.getCaesarVideoWall().getSelected().set(newValue);
            }
          }
        }));
    selectedColumn.setGraphic(selectAllCheckBox);
    selectedColumn.setCellValueFactory(feat -> feat.getValue().getCaesarVideoWall().getSelected());
    selectedColumn.setCellFactory(CheckBoxTableCell.forTableColumn(selectedColumn));

    TableColumn<CaesarVideoWallFunc, Number> idColumn = new TableColumn<>();
    idColumn.setText("ID");
    idColumn.setCellValueFactory(feat -> new ReadOnlyIntegerWrapper(feat.getValue().getCaesarVideoWall().getId()));

    TableColumn<CaesarVideoWallFunc, String> nameColumn = new TableColumn<>();
    nameColumn.setText(CaesarI18nCommonResource.getString("name"));
    nameColumn.setCellValueFactory(
        (feat) -> new ReadOnlyStringWrapper(feat.getValue().getCaesarVideoWall().getName().getValue()));
    getColumns().addAll(selectedColumn, idColumn, nameColumn);
    setEditable(true);
    setItems(videoWallList);
  }

  /**
   * 刷新视图内容.
   */
  public void refreshData() {
    videoWallList.clear();
    CaesarVideoWallFuncManager videoWallFuncManager = dataConverter.getVideoWallFuncManager();
    CaesarDeviceController controller = dataConverter.getDeviceController();
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      CaesarVideoWallFunc func = videoWallFuncManager.getVideoWallFunc(i);
      if (func != null && controller != null
          && controller.getDataModel().getVpDataModel().getVideoWallData(i).isDataValid()
          && func.getChildren().stream().findFirst().isPresent()) {
        VisualEditNode first = func.getChildren().stream().findFirst().get();
        if (first instanceof VpGroup && !((VpGroup) first).getVpConsoleData().isStatusOnline()) {
          videoWallList.add(func);
        }
      }
    }
  }

  private void delete(CaesarVideoWallFunc item) {
    int index = item.getVideoWallIndex();
    CaesarDeviceController controller = dataConverter.getDeviceController();
    // 删除视频墙
    controller.deleteVideoWall(index);
    // 删除视频墙时删除连接.
    List<VpConsoleData> vpConsoleDataList = new ArrayList<>();
    for (VisualEditNode child : item.getChildren()) {
      if (child instanceof VpGroup) {
        vpConsoleDataList.add(((VpGroup) child).getVpConsoleData());
      }
    }
    controller.resetVpCons(vpConsoleDataList);
    CaesarVideoWallFuncManager videoWallFuncManager = dataConverter.getVideoWallFuncManager();
    videoWallFuncManager.setVideoWallFunc(index, null);
    // 列表删除该项
    PlatformUtility.runInFxThreadLater(() -> videoWallList.remove(item));
  }

  private void delete() {
    FilteredList<CaesarVideoWallFunc> selectedConList =
        videoWallList.filtered(data -> data.getCaesarVideoWall().getSelected().get());
    if (selectedConList.isEmpty()) {
      showEmptyAlert(getScene().getWindow());
      return;
    }
    AlertEx confirm = new UndecoratedAlert(AlertEx.AlertExType.WARNING);
    confirm.initOwner(getScene().getWindow());
    confirm.setHeaderText(null);
    confirm.getButtonTypes().add(ButtonType.CANCEL);
    confirm.setContentText(CaesarI18nCommonResource.getString("offline_manager.delete_checked.alert"));
    confirm.showAndWait().ifPresent(response -> {
      if (response == ButtonType.OK) {
        for (CaesarVideoWallFunc videoWallFunc : selectedConList) {
          delete(videoWallFunc);
        }
      }
    });
  }

  private void showEmptyAlert(Window owner) {
    AlertEx alert = new UndecoratedAlert(AlertEx.AlertExType.WARNING);
    alert.initOwner(owner);
    alert.setTitle(CaesarI18nCommonResource.getString("offline_manager.empty_alert.title"));
    alert.setHeaderText(null);
    alert.setContentText(CaesarI18nCommonResource.getString("offline_manager.empty_alert.text"));
    alert.showAndWait();
  }
}
