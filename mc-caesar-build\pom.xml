<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.mc.tool.caesar</groupId>
  <artifactId>mc-caesar-build</artifactId>
  <version>${revision}</version>
  <packaging>pom</packaging>
  <modules>
    <module>../Undecorator</module>
    <module>../mc-common</module>
    <module>../mc-graph</module>
    <module>../mc-framework</module>
    <module>../mc-caesar-api</module>
    <module>../mc-caesar-vpm</module>
  </modules>
  <properties>
    <revision>5.2.0-beta1</revision>
    <mc.framework.version>0.0.1-SNAPSHOT</mc.framework.version>
    <mc.common.version>0.0.1-SNAPSHOT</mc.common.version>
  </properties>
  <build>
    <plugins>
      <plugin>
        <groupId>org.owasp</groupId>
        <artifactId>dependency-check-maven</artifactId>
        <version>8.2.1</version>
        <configuration>
          <suppressionFiles>
            <suppressionFile>owasp-dependency-check-suppressions.xml</suppressionFile>
          </suppressionFiles>
          <autoUpdate>true</autoUpdate>
          <failBuildOnCVSS>7</failBuildOnCVSS>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>