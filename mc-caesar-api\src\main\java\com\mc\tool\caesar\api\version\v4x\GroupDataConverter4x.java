package com.mc.tool.caesar.api.version.v4x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.TxGroupData;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;

/**
 * .
 */
public class GroupDataConverter4x implements ApiDataConverter<TxGroupData> {

  private final int dataSize;

  public GroupDataConverter4x(int dataSize) {
    this.dataSize = dataSize;
  }

  @Override
  public void readData(TxGroupData data, CfgReader cfgReader) throws ConfigException {
    final int startAvailable = cfgReader.available();
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    data.setName(name);
    int offset = cfgReader.readByteValue();
    data.setStatus(offset & 1);
    int endAvailable = cfgReader.available();
    int readedSize = startAvailable - endAvailable;
    int reserveSize = dataSize - readedSize;
    cfgReader.readByteArray(reserveSize);
  }

  @Override
  public void writeData(TxGroupData data, CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    cfgWriter.writeString(data.getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeByte((byte) (data.getStatus()));
    long endSize = cfgWriter.getSize();
    int writedSize = (int) (endSize - startSize);
    int reservedSize = dataSize - writedSize;
    cfgWriter.writeByteArray(new byte[reservedSize]);
  }
}
