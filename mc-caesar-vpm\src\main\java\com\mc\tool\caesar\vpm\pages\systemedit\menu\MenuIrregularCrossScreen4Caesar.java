package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarIrregularCrossScreenFunc;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.menu.MenuGroup;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.menu.predicate.TypePredicate;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedDialog;
import com.sun.javafx.scene.control.skin.resources.ControlResources;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.function.Predicate;
import java.util.function.UnaryOperator;
import javafx.beans.binding.Bindings;
import javafx.geometry.Pos;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.ButtonType;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextFormatter.Change;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.stage.Modality;
import javafx.stage.Window;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class MenuIrregularCrossScreen4Caesar extends MenuItem {
  protected final SystemEditControllable controllable;
  protected final CaesarDeviceController deviceController;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuIrregularCrossScreen4Caesar(
      SystemEditControllable controllable, CaesarDeviceController deviceController) {
    this.controllable = controllable;
    this.deviceController = deviceController;
    this.setText(
        I18nUtility.getI18nBundle("systemedit").getString("menu.create_irregular_cross_screen"));
    this.setOnAction(event -> onAction());
    this.disableProperty().bind(getMenuDisableBinding(controllable));
  }

  /**
   * 获取菜单是否应该禁掉的binding.
   *
   * @param controllable controllable
   * @return binding
   */
  public static MenuPredicateBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = MenuGroup.getMenuDisableBinding(controllable);
    Predicate<VisualEditNode> notTypePredicate =
        new TypePredicate(VisualEditTerminal.class).negate();
    binding.addSingleSelectionPredicate(notTypePredicate);
    binding.addSingleSelectionPredicate(VisualEditNode::isTx);
    binding.addSingleSelectionPredicate(node -> !(node instanceof CaesarConTerminal));
    binding.addSingleSelectionPredicate(
        node -> node instanceof CaesarConTerminal && ((CaesarConTerminal) node).isVp());
    binding.addAllSelectionPredicate((nodes) -> nodes.size() > CaesarConstants.MultiScreen.MAX_CON);
    binding.addSingleSelectionPredicate(
        node -> node instanceof CaesarConTerminal && ((CaesarConTerminal) node).isVp());
    binding.addSingleSelectionPredicate(node -> node instanceof CaesarConTerminal
        && ((CaesarConTerminal) node).getExtenderData().getExtenderStatusInfo().getSpecialExtType()
        == CaesarConstants.Extender.SpecialExtenderType.HW_PREVIEW);
    return binding;
  }

  protected void onAction() {
    Collection<CellSkin> skins = controllable.getGraph().getSelectionModel().getSelectedCellSkin();
    List<VisualEditNode> nodeList = new ArrayList<>();
    for (CellSkin skin : skins) {
      if (skin.getCell().getBindedObject() instanceof VisualEditNode) {
        nodeList.add((VisualEditNode) skin.getCell().getBindedObject());
      } else {
        log.warn(
            "Cell's bindedobject is not VisualEditNode but {}", skin.getCell().getBindedObject());
      }
    }
    MultiScreenData screenData =
        deviceController.getDataModel().getConfigDataManager().getFreeMultiScreenData();
    if (screenData == null) {
      ViewUtility.showAlert(
          getParentPopup().getOwnerWindow(),
          CaesarI18nCommonResource.getString("cross_screen.fail_to_create"),
          AlertExType.WARNING);
      return;
    }
    Optional<CrossScreenInfo> result =
        getCrossScreenInfoFromDialog(getParentPopup().getOwnerWindow(), "", nodeList.size());
    if (result.isPresent()) {
      CaesarIrregularCrossScreenFunc func =
          controllable.addGroup(
              result.get().getName(),
              CaesarIrregularCrossScreenFunc.class,
              nodeList.toArray(new VisualEditNode[0]));
      if (func != null) {
        try {
          if (deviceController
              .submit(
                  () -> {
                    screenData.setStatusActive(true);
                    screenData.setStatusNew(true);
                    return true;
                  })
              .get()) {
            func.setDeviceData(screenData);
            func.setLayout(result.get().getRow() + 2, result.get().getColumn() + 2);
            // 监听名称修改
            func.addNameListener(deviceController);
          }

          // 提示切换到配置页面
          Optional<ButtonType> askResult =
              ViewUtility.getConfirmResultFromDialog(
                  getParentPopup().getOwnerWindow(),
                  CaesarI18nCommonResource.getString("page.switch"),
                  CaesarI18nCommonResource.getString("page.switch_to_operation"));
          if (askResult.isPresent() && askResult.get() == ButtonType.YES) {
            ApplicationBase applicationBase =
                InjectorProvider.getInjector().getInstance(ApplicationBase.class);
            applicationBase
                .getViewManager()
                .switchToPage(controllable.getEntity(), CaesarOperationPageNew.NAME, func);
          }
        } catch (ExecutionException | InterruptedException exception) {
          log.warn("Fail to set MultiScreenData Status.", exception);
        }
      }
    }
  }

  protected Optional<CrossScreenInfo> getCrossScreenInfoFromDialog(
      Window owner, String initText, int minCount) {
    NameDialog dialog = new NameDialog(minCount, initText);
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.initOwner(owner);
    dialog.setTitle(
        I18nUtility.getI18nBundle("systemedit").getString("cell.wizard.nameconfig_title"));
    dialog.setHeaderText(null);
    dialog.setContentText(
        I18nUtility.getI18nBundle("systemedit").getString("cell.wizard.nameconfig_content"));
    dialog.setGraphic(null);
    dialog.getDialogPane().setMinWidth(300);

    return dialog.showAndWait();
  }

  static class CrossScreenInfo {
    @Getter @Setter private String name;
    @Getter @Setter private int row;
    @Getter @Setter private int column;
  }

  static class NameDialog extends UndecoratedDialog<CrossScreenInfo> {
    private TextField textField;
    private TextField rowTextField;
    private TextField columnTextField;
    private final int minCount;

    public NameDialog(int minCount, String defaultValue) {
      this.minCount = minCount;
      textField = new TextField(defaultValue);
      textField.setId("cross-screen-name");
      textField.setMaxWidth(Double.MAX_VALUE);
      GridPane.setHgrow(textField, Priority.ALWAYS);
      GridPane.setFillWidth(textField, true);

      // -- label
      Label label = DialogPaneEx.createContentLabel(getDialogPane().getContentText());
      label.textProperty().bind(getDialogPane().contentTextProperty());
      label.setPrefWidth(Region.USE_COMPUTED_SIZE);

      Label rowLabel =
          DialogPaneEx.createContentLabel(CaesarI18nCommonResource.getString("cross_screen.row"));
      rowLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);

      Label columnLabel =
          DialogPaneEx.createContentLabel(
              CaesarI18nCommonResource.getString("cross_screen.column"));
      columnLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);

      rowTextField = new TextField();
      rowTextField.setId("cross-screen-row");
      rowTextField.setMaxWidth(Double.MAX_VALUE);
      GridPane.setHgrow(rowTextField, Priority.ALWAYS);
      GridPane.setFillWidth(rowTextField, true);
      UnaryOperator<Change> integerFilter =
          change -> {
            String newText = change.getControlNewText();
            if (newText.matches("[0-9]*")) {
              return change;
            }
            return null;
          };
      rowTextField.setTextFormatter(new TextFormatter<>(integerFilter));

      columnTextField = new TextField();
      columnTextField.setId("cross-screen-column");
      columnTextField.setMaxWidth(Double.MAX_VALUE);
      GridPane.setHgrow(columnTextField, Priority.ALWAYS);
      GridPane.setFillWidth(columnTextField, true);
      columnTextField.setTextFormatter(new TextFormatter<>(integerFilter));

      GridPane grid = new GridPane();
      grid.setHgap(10);
      grid.setAlignment(Pos.CENTER_LEFT);
      grid.setMaxWidth(Double.MAX_VALUE);

      grid.add(label, 0, 0);
      grid.add(textField, 1, 0);

      grid.add(rowLabel, 0, 1);
      grid.add(rowTextField, 1, 1);

      grid.add(columnLabel, 0, 2);
      grid.add(columnTextField, 1, 2);

      getDialogPane().setContent(grid);
      setTitle(ControlResources.getString("Dialog.confirm.title"));
      getDialogPane().getStyleClass().add("text-input-dialog");
      getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
      setGraphic(null);
      setHeaderText(null);

      getDialogPane()
          .lookupButton(ButtonType.OK)
          .disableProperty()
          .bind(
              Bindings.createBooleanBinding(
                  () -> {
                    int row;
                    int column;
                    try {
                      row = Integer.parseInt(rowTextField.getText());
                      column = Integer.parseInt(columnTextField.getText());
                    } catch (NumberFormatException exception) {
                      return true;
                    }

                    return textField.getText().length() <= 0
                        || row * column < this.minCount
                        || row > 99
                        || column > 99;
                  },
                  textField.textProperty(),
                  rowTextField.textProperty(),
                  columnTextField.textProperty()));

      setResultConverter(
          (type) -> {
            if (type == ButtonType.OK) {
              CrossScreenInfo info = new CrossScreenInfo();
              info.setName(textField.getText());
              info.setColumn(Integer.parseInt(columnTextField.getText()));
              info.setRow(Integer.parseInt(rowTextField.getText()));
              return info;
            } else {
              return null;
            }
          });
    }
  }
}
