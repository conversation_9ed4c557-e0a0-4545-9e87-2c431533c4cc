package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import com.dooapp.fxform.FXForm;
import com.dooapp.fxform.builder.FXFormBuilder2;
import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.MatrixStatus;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.vpm.devices.CaesarDemoDeviceController;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.activateconfig.ActivateConfigPageView;
import com.mc.tool.framework.utility.InjectorProvider;
import java.beans.PropertyChangeListener;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class CaesarHostConfigurationPageView extends VBox {
  @Getter private final CaesarHostConfigurationController controller;

  private final WeakAdapter weakAdapter = new WeakAdapter();

  /** Constructor. */
  @SuppressWarnings("unchecked")
  public CaesarHostConfigurationPageView(CaesarDeviceController caesarDeviceController) {
    controller =
        InjectorProvider.getInjector().getInstance(CaesarHostConfigurationController.class);
    // 通用配置
    LocalDateTime time = LocalDateTime.now();
    SystemConfigData systemConfigData =
        caesarDeviceController.getDataModel().getConfigData().getSystemConfigData();
    GeneralConfigurationBean gcBean = new GeneralConfigurationBean(systemConfigData, time);

    URL generalUrl =
        getClass().getResource("/com/mc/tool/caesar/vpm/pages/hostconfiguration/generalForm.fxml");
    ResourceBundle resourceBundle =
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.hostconfiguration.bean");
    ResourceBundle extraResourceBundle =
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.hostconfiguration.bundle");
    GeneralFormController generalFormController = new GeneralFormController();
    FXForm<GeneralConfigurationBean> generalForm =
        new FXFormBuilder2()
            .source(gcBean)
            .buffered(true, false)
            .resourceBundle(resourceBundle)
            .extraResourceBundle(extraResourceBundle)
            .fxml(generalUrl)
            .exclude("systemConfigData")
            .controller(generalFormController)
            .build();
    controller.setGeneralForm(generalForm);

    // 网络配置
    List<String> include = new ArrayList<>();
    ModuleData module = caesarDeviceController.getDataModel().getSwitchModuleData().getModuleData(0);
    if (module != null) {
      CaesarConstants.Module.Type type = CaesarConstants.Module.Type.valueOf(module.getType());
      if (type == null || CaesarConstants.Module.Type.PLUGIN_384.equals(type)
          || CaesarConstants.Module.Type.PLUGIN_816.equals(type)
          || CaesarConstants.Module.Type.PLUGIN_144.equals(type)) {
        include.add(NetworkConfigurationBean.DUAL_MASTER_VIRTUAL_IP);
        include.add(NetworkConfigurationBean.DUAL_MASTER_VIRTUAL_ROUTE_ID);
      }
    }
    include.add(NetworkConfigurationBean.IP_ADDRESS_1);
    include.add(NetworkConfigurationBean.NETMASK_1);
    include.add(NetworkConfigurationBean.GATEWAY_1);
    include.add(NetworkConfigurationBean.MAC_ADDRESS_1);
    MatrixStatus matrixStatus =
        caesarDeviceController.getDataModel().getConfigData().getMatrixStatus();
    if (caesarDeviceController instanceof CaesarDemoDeviceController
        || matrixStatus != null && matrixStatus.isDoubleBackup()) {
      include.add(NetworkConfigurationBean.IP_ADDRESS_2);
      include.add(NetworkConfigurationBean.NETMASK_2);
      include.add(NetworkConfigurationBean.GATEWAY_2);
    }
    NetworkConfigurationBean ncBean = new NetworkConfigurationBean(systemConfigData);
    URL networkUrl =
        getClass().getResource("/com/mc/tool/caesar/vpm/pages/hostconfiguration/networkForm.fxml");
    NetworkFormController networkFormController = new NetworkFormController(caesarDeviceController);
    FXFormBuilder2 networkFormBuilder = new FXFormBuilder2<>()
        .source(ncBean).buffered(true, false)
        .resourceBundle(resourceBundle)
        .extraResourceBundle(extraResourceBundle)
        .fxml(networkUrl)
        .controller(networkFormController);
    networkFormBuilder.includeAndReorder(include.toArray(new String[0]));
    FXForm<NetworkConfigurationBean> networkForm = networkFormBuilder.build();
    controller.setNetworkForm(networkForm);

    // SNMP配置
    SnmpConfigurationBean scBean = new SnmpConfigurationBean(systemConfigData);
    URL snmpUrl =
        getClass().getResource("/com/mc/tool/caesar/vpm/pages/hostconfiguration/snmpForm.fxml");
    FXForm<SnmpConfigurationBean> snmpForm =
        new FXFormBuilder2()
            .source(scBean)
            .buffered(true, false)
            .resourceBundle(resourceBundle)
            .extraResourceBundle(extraResourceBundle)
            .fxml(snmpUrl)
            .exclude("systemConfigData")
            .build();
    controller.setSnmpForm(snmpForm);

    // 双机冗余
    RedundancyBean rdBean = new RedundancyBean(systemConfigData);
    URL rdUrl =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource("com/mc/tool/caesar/vpm/pages/hostconfiguration/redundancyForm.fxml");
    FXForm<RedundancyBean> rdForm =
        new FXFormBuilder2()
            .source(rdBean)
            .buffered(true, false)
            .resourceBundle(resourceBundle)
            .extraResourceBundle(extraResourceBundle)
            .fxml(rdUrl)
            .exclude("systemConfigData")
            .build();
    controller.setRdForm(rdForm);

    // 事件配置
    EventConfigurationBean eventBean = new EventConfigurationBean(systemConfigData);
    URL eventUrl =
        getClass().getResource("/com/mc/tool/caesar/vpm/pages/hostconfiguration/eventForm.fxml");
    EventFormController eventFormController = new EventFormController(caesarDeviceController);
    FXForm<EventConfigurationBean> eventForm =
        new FXFormBuilder2()
            .source(eventBean)
            .buffered(true, false)
            .resourceBundle(resourceBundle)
            .extraResourceBundle(extraResourceBundle)
            .fxml(eventUrl)
            .controller(eventFormController)
            .exclude("systemConfigData")
            .build();
    controller.setEventForm(eventForm);

    // 激活配置
    ActivateConfigPageView activateConfigPane = new ActivateConfigPageView();
    activateConfigPane.getController().setDeviceController(caesarDeviceController);
    controller.setActivateConfigPane(activateConfigPane);

    URL location =
        getClass()
            .getResource(
                "/com/mc/tool/caesar/vpm/pages/hostconfiguration/hostconfiguration_view.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setResources(ResourceBundle.getBundle("com.mc.tool.caesar.vpm.i18n.common"));
    loader.setController(controller);
    loader.setRoot(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load hostconfiguration_view.fxml", exception);
    }

    caesarDeviceController.getDataModel()
        .addPropertyChangeListener(weakAdapter.wrap((PropertyChangeListener)
            change -> PlatformUtility.runInFxThread(() -> {
              generalForm.refresh();
              networkForm.refresh();
              snmpForm.refresh();
            })));
  }
}
