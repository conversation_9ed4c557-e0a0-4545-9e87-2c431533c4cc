package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.api.utils.ExtendedSwitchUtility;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarGridLineTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbRxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.menu.predicate.DirectionNotSamePredicate;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.menu.predicate.TypePredicate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.collections.ObservableList;
import javafx.scene.control.MenuItem;

/**
 * .
 */
public class MenuDisconnect extends MenuItem {
  private CaesarDeviceController deviceController;
  private SystemEditControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   * @param deviceController device controller
   */
  public MenuDisconnect(
      SystemEditControllable controllable, CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
    this.controllable = controllable;
    this.setOnAction(event -> onAction());
    setText(Bundle.NbBundle.getMessage("MenuDisconnect"));
    this.disableProperty().bind(getMenuDisableBinding());
  }

  private MenuPredicateBinding getMenuDisableBinding() {
    MenuPredicateBinding binding = new MenuPredicateBinding(controllable, true);
    binding.addSingleSelectionPredicate((node) -> node.getParent() instanceof VpGroup);
    binding.addSingleSelectionPredicate(new TypePredicate(VisualEditGroup.class));
    binding.addAllSelectionPredicate(new DirectionNotSamePredicate());
    binding.addSingleSelectionPredicate(new TypePredicate(CaesarMatrix.class));
    binding.addSingleSelectionPredicate(new TypePredicate(CaesarUsbTxTerminal.class));
    binding.addSingleSelectionPredicate(new TypePredicate(CaesarUsbRxTerminal.class));
    binding.addSingleSelectionPredicate(new TypePredicate(CaesarGridLineTerminal.class));
    return binding;
  }

  private void onAction() {
    Collection<VisualEditNode> nodes = controllable.getSelectedNodes();
    ObservableList<VisualEditConnection> connectionList = controllable.getModel().getConnections();
    List<VisualEditConnection> list = new ArrayList<>();
    if (!nodes.isEmpty()) {
      for (VisualEditNode node : nodes) {
        if (node instanceof CaesarConTerminal) {
          CaesarConTerminal terminal = (CaesarConTerminal) node;
          deviceController.execute(() ->
              ExtendedSwitchUtility.disconnect(
                  deviceController.getDataModel(), terminal.getConsoleData(), true));
          for (VisualEditConnection connection : connectionList) {
            if (terminal == connection.getRxTerminal()) {
              list.add(connection);
            }
          }
        } else if (node instanceof CaesarCpuTerminal) {
          CaesarCpuTerminal terminal = (CaesarCpuTerminal) node;
          deviceController.execute(() ->
              ExtendedSwitchUtility.disconnect(
                  deviceController.getDataModel(), terminal.getCpuData(), true));
          for (VisualEditConnection connection : connectionList) {
            if (terminal == connection.getTxTerminal()) {
              list.add(connection);
            }
          }
        }
      }
      controllable.getModel().removeConnections(list.toArray(new VisualEditConnection[0]));
    }
  }
}
