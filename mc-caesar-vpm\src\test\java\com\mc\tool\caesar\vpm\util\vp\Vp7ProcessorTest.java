package com.mc.tool.caesar.vpm.util.vp;

import com.google.common.io.CharStreams;
import com.google.gson.Gson;
import com.mc.common.lang.reflect.ParameterizedTypeImpl;
import com.mc.tool.caesar.api.SwitchMultiviewAllVideoChannel;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.videowall.vp.CaesarVpMatrix;
import com.mc.tool.caesar.vpm.pages.operation.videowall.vp.CaesarVpVideoWall;
import com.mc.tool.caesar.vpm.util.VideoWallGenerator;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.lang.reflect.Type;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.runners.Enclosed;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

@RunWith(Enclosed.class)
public class Vp7ProcessorTest {

  static class TestBase {

    @Before
    public void before() {
      System.setProperty("generator-mode", "true");
      System.setProperty("no-fx-mode", "true");
    }


    protected void checkResult(ClassLoader classLoader, Vp7ConfigData configData, String resultPath) {
      System.out.println(configData.toString());
      InputStream resultStream = classLoader.getResourceAsStream(resultPath);
      Reader resultReader = new InputStreamReader(resultStream);
      try {
        String result = CharStreams.toString(resultReader);
        Assert.assertEquals(result, configData.formatAvConfig());
      } catch (IOException e) {
        Assert.fail();
      }
    }

    protected void replaceResult(ClassLoader classLoader, Vp7ConfigData configData, String resultPath) {
      String result = configData.formatAvConfig();
      URL url = classLoader.getResource(resultPath);
      // 把result写入url
      try (OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(new File(url.toURI())))) {
        writer.write(result);
      } catch (IOException | URISyntaxException e) {
        Assert.fail();
      }
    }


    protected void checkConnections(
        CaesarDeviceController controller,
        ClassLoader classLoader,
        List<SwitchMultiviewAllVideoChannel> channels,
        String path) {
      InputStream resultStream = classLoader.getResourceAsStream(path);
      if (resultStream == null) {
        return;
      }
      Reader resultReader = new InputStreamReader(resultStream);
      try {
        String result = CharStreams.toString(resultReader);
        Map<String, Integer[]> connectTxIds =
            new Gson()
                .fromJson(
                    result,
                    new ParameterizedTypeImpl(Map.class, new Type[]{String.class, Integer[].class}));

        // 收集tx的索引
        int index = 0;
        Map<Integer, Integer> indexMapTxId = new HashMap<>();
        for (CpuData cpuData : controller.getDataModel().getConfigDataManager().getActiveCpus()) {
          indexMapTxId.put(index, cpuData.getId());
          index++;
        }
        // 对比数据
        List<Integer> expectedList =
            Arrays.stream(connectTxIds.get("connectTxIds")).map((idx) -> indexMapTxId.get(idx)).collect(Collectors.toList());
        List<Integer> actualList = channels.stream().map(
                (SwitchMultiviewAllVideoChannel channel) -> channel.getCpuData() == null ? 0 : channel.getCpuData().getId())
            .collect(Collectors.toList());
        Assert.assertEquals(expectedList, actualList);
      } catch (IOException e) {
        Assert.fail();
      }
    }
  }

  public static class TestWithoutParams extends TestBase {

    @Test
    public void testAllConfig() {
      ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
      String path = "com/mc/tool/caesar/vpm/videowall/unit/common/input/vp7/full.json";
      String resultPath = "com/mc/tool/caesar/vpm/videowall/unit/common/result/vp7/full.allconfig.txt";
      VideoWallGenerator videoWallGenerator =
          VideoWallGenerator.createVideoWallGeneratorFromResource(classLoader, path, VideoWallGenerator.VideoWallType.VP7);
      CaesarVpMatrix vpMatrix = new CaesarVpMatrix(videoWallGenerator.getController());
      CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
      Vp7WallCurrCfg vp7WallCurrCfg = new Vp7WallCurrCfg();
      Vp7Processor.Status status = Vp7Processor.processVideoWall(vpMatrix, vpVideoWall, vp7WallCurrCfg);
      Assert.assertEquals(Vp7Processor.Status.SUCCESS, status);
      Assert.assertTrue(vp7WallCurrCfg.getUsedVpCons().size() > 0);
      Vp7ConfigData configData = vp7WallCurrCfg.getVpconConfigData(vp7WallCurrCfg.getUsedVpCons().get(0));

      System.out.println(configData.toString());
      InputStream resultStream = classLoader.getResourceAsStream(resultPath);
      Reader resultReader = new InputStreamReader(resultStream);
      try {
        String result = CharStreams.toString(resultReader);
        Assert.assertEquals(result, configData.toString());
      } catch (IOException e) {
        Assert.fail();
      }
    }

    @Test
    public void testRedundant() {
      ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
      String path = "com/mc/tool/caesar/vpm/videowall/unit/common/input/vp7/full.json";
      VideoWallGenerator videoWallGenerator =
          VideoWallGenerator.createVideoWallGeneratorFromResource(classLoader, path, VideoWallGenerator.VideoWallType.VP7);
      // 使能冗余
      videoWallGenerator.getVideoWallData().getOsdData().setEnableRedundant(true);
      CaesarVpMatrix vpMatrix = new CaesarVpMatrix(videoWallGenerator.getController());
      CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
      Vp7WallCurrCfg vp7WallCurrCfg = new Vp7WallCurrCfg();
      Vp7Processor.Status status = Vp7Processor.processVideoWall(vpMatrix, vpVideoWall, vp7WallCurrCfg);
      Assert.assertEquals(Vp7Processor.Status.SUCCESS, status);
      Assert.assertTrue(vp7WallCurrCfg.getUsedVpCons().size() > 0);
      checkResult(classLoader, vp7WallCurrCfg.getVpconConfigData(vp7WallCurrCfg.getUsedVpCons().get(0)),
          "com/mc/tool/caesar/vpm/videowall/unit/common/result/vp7/full.redundant.txt");
    }

    @Test
    public void testRedundantWithoutLayers() {
      ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
      // 无窗口大屏
      VideoWallGenerator videoWallGenerator = VideoWallGenerator.createNoWindowVideoWallGenerator(VideoWallGenerator.VideoWallType.VP7);
      // 使能冗余
      videoWallGenerator.getVideoWallData().getOsdData().setEnableRedundant(true);
      CaesarVpMatrix vpMatrix = new CaesarVpMatrix(videoWallGenerator.getController());
      CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
      Vp7WallCurrCfg vp7WallCurrCfg = new Vp7WallCurrCfg();
      Vp7Processor.Status status = Vp7Processor.processVideoWall(vpMatrix, vpVideoWall, vp7WallCurrCfg);
      Assert.assertEquals(Vp7Processor.Status.SUCCESS, status);
      Assert.assertTrue(vp7WallCurrCfg.getUsedVpCons().size() > 0);
      checkResult(classLoader, vp7WallCurrCfg.getVpconConfigData(vp7WallCurrCfg.getUsedVpCons().get(0)),
          "com/mc/tool/caesar/vpm/videowall/unit/common/result/vp7/empty.redundant.txt");
    }

  }

  @RunWith(Parameterized.class)
  public static class TestWithParams extends TestBase {

    @Parameterized.Parameters(name = "{0}")
    public static Object[] data() {
      return new Object[]{
          "2k_full_odd_size_screen",
          "2k_odd_size_pos",
          "2k_full_irregular_screen",
          "4k30_odd_size_pos",
          "4k60x1_4k30x1",
          "4k60x1_dhdmix1",
          "4k60x1_2kx1",
          "4k60x4",
          "4k60_full",
//            "clip_2k_with_compensation",
//            "2k_with_compensation_disable_top_full",
//            "2k_with_compensation_disable_left_full",
//            "2k_with_compensation_scale_not_enough_full",
//            "2k_with_compensation_disable_full",
//            "2k_with_compensation_oversize",
//            "2k_with_compensation_center",
//            "2k_with_compensation_3x3_full",
//            "2k_with_compensation_full",
            "clip_4k_error_width",
            "clip_2k_error_height",
            "clip_2k_error_width",
            "clip_4k_small_right",
            "clip_4k_small_left",
            "clip_4k_bottom_left",
            "clip_4k_bottom_right",
            "clip_4k_top_right",
            "clip_4k_top_left",
            "clip_2k_bottom_left",
            "clip_2k_bottom_right",
            "clip_2k_top_right",
            "clip_2k_top_left",
//            "4k_full_with_positive_margin",
//            "4k_full_with_negative_margin",
//            "2_vp6_cross",
//            "2k_global_more_layer",
//            "dhdmi_right",
//            "dhdmi_left",
//            "dhdmi_dual_revert",
          "dhdmi_dual",
//            "4k_2k_more_layer",
//            "4k_2k_more_window",
//            "4k_4_window_right_more_layer",
//            "4k_4_window_left_more_layer",
          "4k_4_window_no_more_layer",
//            "4k_more_layer",
//            "4k_more_window",
//            "2k_more_layer",
//            "2k_more_window",
          "4k_full",
//            "4k_full_3800",
          "full"
      };
    }

    public TestWithParams(String config) {
      this.config = config;
    }

    private final String config;

    @Test
    public void testCommonConfig() {
      ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
      System.out.println("Testing " + config + ".json");
      String path = "com/mc/tool/caesar/vpm/videowall/unit/common/input/vp7/" + config + ".json";
      VideoWallGenerator videoWallGenerator =
          VideoWallGenerator.createVideoWallGeneratorFromResource(classLoader, path, VideoWallGenerator.VideoWallType.VP7);

      CaesarVpMatrix vpMatrix = new CaesarVpMatrix(videoWallGenerator.getController());
      CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
      Vp7WallCurrCfg vp7WallCurrCfg = new Vp7WallCurrCfg();
      Vp7Processor.processVideoWall(vpMatrix, vpVideoWall, vp7WallCurrCfg);

      List<VpConsoleData> vpcons =
          new ArrayList<>(
              videoWallGenerator
                  .getController()
                  .getDataModel()
                  .getConfigDataManager()
                  .getActiveVpconsolses());
      Map<Integer, Integer> id2Idx =
          IntStream.range(0, vpcons.size())
              .boxed()
              .collect(Collectors.toMap((idx) -> vpcons.get(idx).getInPort(0).getId(), (idx) -> idx));
      Assert.assertTrue(vp7WallCurrCfg.getUsedVpCons().size() > 0);
      for (int vpconId : vp7WallCurrCfg.getUsedVpCons()) {
        Vp7ConfigData configData = vp7WallCurrCfg.getVpconConfigData(vpconId);
        int index = id2Idx.get(vpconId);
        String cfgResultPath =
            "com/mc/tool/caesar/vpm/videowall/unit/common/result/vp7/"
                + config
                + (index == 0 ? "" : "_" + index)
                + ".txt";
        checkResult(classLoader, configData, cfgResultPath);

        String connectionResultPath =
            "com/mc/tool/caesar/vpm/videowall/unit/common/result/vp7/" + config + (index == 0 ? "" : "_" + index) + ".port.json";
        VpConsoleData vpConsoleData = vpMatrix.findVpCon(vpconId);
        List<SwitchMultiviewAllVideoChannel> channels = Vp7Processor.getConnections(vpMatrix, vpConsoleData, vp7WallCurrCfg);
        checkConnections(videoWallGenerator.getController(), classLoader, channels, connectionResultPath);
      }


    }
  }

}
