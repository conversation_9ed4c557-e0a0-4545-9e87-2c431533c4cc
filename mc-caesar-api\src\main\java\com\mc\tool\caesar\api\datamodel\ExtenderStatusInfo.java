package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.Extender.ExtenderInterfaceType;
import com.mc.tool.caesar.api.CaesarConstants.Extender.ExtenderVideoResolutionType;
import com.mc.tool.caesar.api.CaesarConstants.Extender.SpecialExtenderType;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.Utilities;
import java.util.BitSet;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
@Getter
public class ExtenderStatusInfo {

  static final int DATA_SIZE = 16;

  @Expose
  private boolean mouseOnline = false;
  @Expose
  private boolean keyboardOnline = false;
  @Expose
  private boolean usbcableOnline = false;
  @Expose
  private boolean udiskOnline = false;
  @Expose
  private boolean redundant = false;
  @Expose
  private boolean link1 = true;
  @Expose
  private boolean vp7 = false;
  @Getter
  @Expose
  private boolean vp6 = false;
  @Expose
  private boolean isVpSdcardEnable = false;
  @Setter
  @Expose
  private ExtenderInterfaceType interfaceType = ExtenderInterfaceType.INTERFACE_TYPE_UNKNOWN;
  @Setter
  @Expose
  private int interfaceCount = 1;
  @Expose
  @Setter
  private ExtenderVideoResolutionType videoResolutionType = ExtenderVideoResolutionType.VIDEO_2K;
  @Expose
  @Setter
  private SpecialExtenderType specialExtType  = SpecialExtenderType.HW_COMMON;
  @Expose
  private ExtenderVideoCh videoCableOnline = new ExtenderVideoCh();
  @Expose
  private ExtenderVideoCh videoInput = new ExtenderVideoCh();
  @Expose
  private ExtenderVideoCh edidvalid = new ExtenderVideoCh();
  @Expose
  private HwSpecial hwSpecial = new HwSpecial();
  @Expose
  private int specialExtSubType = 0;

  /**
   * 读取数据.
   *
   * @param reader reader
   * @throws ConfigException 读取出错
   */
  public void readData(CfgReader reader) throws ConfigException {
    // 如果只有4个字节，按旧的方式解析
    if (reader.available() == 4) {
      readData(reader.readInteger());
      return;
    }
    final int startAvailable = reader.available();
    int firstByte = reader.readByteValue();
    mouseOnline = (firstByte & 0x01) != 0;
    keyboardOnline = (firstByte & 0x02) != 0;
    usbcableOnline = (firstByte & 0x04) != 0;
    udiskOnline = (firstByte & 0x08) != 0;
    redundant = (firstByte & 0x10) != 0;
    link1 = (firstByte & 0x20) == 0;
    vp7 = (firstByte & 0x40) != 0;
    vp6 = (firstByte & 0x80) != 0;

    int secondByte = reader.readByteValue();
    interfaceType = ExtenderInterfaceType.valueOf(secondByte & 0x0f);
    interfaceCount = (secondByte & 0xf0) >> 4;

    int thirdByte = reader.readByteValue();
    videoResolutionType = ExtenderVideoResolutionType.valueOf(thirdByte & 0x7);
    if (vp6) {
      isVpSdcardEnable = (thirdByte & 0x8) != 0;
    } else {
      specialExtType = SpecialExtenderType.valueOf(thirdByte >> 3 & 0xf);
    }

    videoCableOnline.readData(reader);
    videoInput.readData(reader);
    edidvalid.readData(reader);
    hwSpecial.readData(reader);
    specialExtSubType = reader.readByteValue();
    int endAvailable = reader.available();
    int readedSize = startAvailable - endAvailable;
    reader.readByteArray(DATA_SIZE - readedSize);
  }

  /**
   * 复制数据.
   *
   * @param info 信息
   */
  public void readData(ExtenderStatusInfo info) {
    mouseOnline = info.mouseOnline;
    keyboardOnline = info.keyboardOnline;
    usbcableOnline = info.usbcableOnline;
    udiskOnline = info.udiskOnline;
    redundant = info.redundant;
    link1 = info.link1;
    vp7 = info.vp7;
    vp6 = info.vp6;
    interfaceType = info.interfaceType;
    interfaceCount = info.interfaceCount;
    videoResolutionType = info.videoResolutionType;
    videoCableOnline.readData(info.videoCableOnline);
    videoInput.readData(info.videoInput);
    edidvalid.readData(info.edidvalid);
    hwSpecial.readData(info.hwSpecial);
    specialExtSubType = info.specialExtSubType;
  }

  /**
   * 读取状态.
   *
   * @param status 状态信息
   */
  public void readData(int status) {
    mouseOnline =
        Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.MOUSE_ONLINE);
    keyboardOnline =
        Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.KEYBOARD_ONLINE);
    usbcableOnline =
        Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.USB_CABLE_ONLINE);
    redundant = Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.REDUNDANT);
    link1 = Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.LINK1);
    vp7 = Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.VP7);
    vp6 = Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.VP6);
    udiskOnline =
        Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.UDISK_ONLINE);
    //在旧版信息中只有RX才能知道videocable是否online，TX只能根据VIDEO_INPUT来判断
    if (Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.SCREEN_ONLINE)) {
      videoCableOnline.setCh0(true);
    }
    if (Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.VIDEO_INPUT)) {
      videoCableOnline.setCh0(true);
    }
    videoInput
        .setCh0(Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.VIDEO_INPUT));
    edidvalid
        .setCh0(Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.EDID_VALID));

    boolean isHdmi =
        Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.HDMI_MODE);
    boolean isDvi = Utilities.areBitsSet(status, CaesarConstants.Extender.ExtendedStatus.DVI_MODE);
    if (isHdmi) {
      interfaceType = ExtenderInterfaceType.INTERFACE_TYPE_HDMI;
    } else if (isDvi) {
      interfaceType = ExtenderInterfaceType.INTERFACE_TYPE_DVI;
    } else {
      interfaceType = ExtenderInterfaceType.INTERFACE_TYPE_UNKNOWN;
    }
    interfaceCount = 1;
  }

  /**
   * 写数据.
   *
   * @param cfgWriter writer
   * @throws ConfigException 写出错
   */
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    BitSet bitSet = new BitSet(8);
    bitSet.set(0, mouseOnline);
    bitSet.set(1, keyboardOnline);
    bitSet.set(2, usbcableOnline);
    bitSet.set(3, udiskOnline);
    bitSet.set(4, redundant);
    bitSet.set(5, link1);
    bitSet.set(6, vp7);
    bitSet.set(7, vp6);
    cfgWriter.writeByteArray(bitSet.toByteArray());

    int secondByte = interfaceType.getValue() + (interfaceCount << 4);
    cfgWriter.writeByte((byte) secondByte);
    int thirdByte;
    if (vp6) {
      thirdByte = videoResolutionType.getValue() + (isVpSdcardEnable ? 0x08 : 0);
    } else {
      thirdByte = videoResolutionType.getValue() + specialExtType.getValue() << 3;
    }
    cfgWriter.writeByte((byte) thirdByte);
    videoCableOnline.writeData(cfgWriter);
    videoInput.writeData(cfgWriter);
    edidvalid.writeData(cfgWriter);
    hwSpecial.writeData(cfgWriter);
    cfgWriter.writeByte((byte) specialExtSubType);
    long endSize = cfgWriter.getSize();
    cfgWriter.writeByteArray(new byte[(int) (DATA_SIZE - (endSize - startSize))]);
  }

  /**
   * .
   */
  @Getter
  @Setter
  public static class HwSpecial {

    @Expose
    private boolean lineIn = false;

    @Expose
    private boolean micIn = false;

    @Expose
    private boolean icron = false;

    protected void readData(CfgReader reader) throws ConfigException {
      int value = reader.readByteValue();
      lineIn = (value & 0x01) != 0;
      micIn = (value & 0x02) != 0;
      icron = (value & 0x04) != 0;
    }

    protected void readData(HwSpecial input) {
      lineIn = input.lineIn;
      micIn = input.micIn;
      icron = input.icron;
    }

    protected void writeData(CfgWriter writer) throws ConfigException {
      BitSet bitSet = new BitSet(8);
      bitSet.set(0, lineIn);
      bitSet.set(1, micIn);
      bitSet.set(2, icron);
      writer.writeByteArray(bitSet.toByteArray());
    }
  }

  /**
   * .
   */
  @Setter
  @Getter
  public static class ExtenderVideoCh {

    @Expose
    private boolean ch0 = false;
    @Expose
    private boolean ch1 = false;
    @Expose
    private boolean ch2 = false;
    @Expose
    private boolean ch3 = false;


    /**
     * 读取数据.
     *
     * @param reader reader
     * @throws ConfigException 读取出错
     */
    public void readData(CfgReader reader) throws ConfigException {
      int value = reader.read2ByteValue();
      ch0 = (value & 0x01) != 0;
      ch1 = (value & 0x02) != 0;
      ch2 = (value & 0x04) != 0;
      ch3 = (value & 0x08) != 0;
    }

    /**
     * 读取数据.
     */
    public void readData(ExtenderVideoCh ch) {
      ch0 = ch.ch0;
      ch1 = ch.ch1;
      ch2 = ch.ch2;
      ch3 = ch.ch3;
    }

    /**
     * 写数据.
     *
     * @param writer writer
     * @throws ConfigException 写出错
     */
    public void writeData(CfgWriter writer) throws ConfigException {
      BitSet bitSet = new BitSet(16);
      bitSet.set(0, ch0);
      bitSet.set(1, ch1);
      bitSet.set(2, ch2);
      bitSet.set(3, ch3);
      writer.writeByteArray(bitSet.toByteArray());
    }

    /**
     * 获取通道状态.
     *
     * @param chIndex 通道索引
     * @return 状态
     */
    public boolean getChStatus(int chIndex) {
      if (chIndex == 0) {
        return ch0;
      } else if (chIndex == 1) {
        return ch1;
      } else if (chIndex == 2) {
        return ch2;
      } else if (chIndex == 3) {
        return ch3;
      } else {
        return false;
      }
    }
  }
}
