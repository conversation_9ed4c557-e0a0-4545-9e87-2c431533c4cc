package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.Console.SuspendPosition;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.extargs.DhdmiSelection;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.NamepropertyAble;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.AdvancedBitSet;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import java.beans.IndexedPropertyChangeEvent;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public final class ConsoleData extends AbstractData implements DataObject, AccessControlObject,
    FavoriteObject, CaesarCommunicatable, NamepropertyAble {

  private static final Logger LOG = Logger.getLogger(ConsoleData.class.getName());
  public static final String PROPERTY_BASE = "ConsoleData.";
  public static final String FIELD_NAME = "Name";
  public static final String PROPERTY_NAME = "ConsoleData.Name";
  public static final String FIELD_STATUS = "Status";
  public static final String PROPERTY_STATUS = "ConsoleData.Status";
  public static final String PROPERTY_STATUS_ACTIVE = "ConsoleData.Status.Active";
  public static final String PROPERTY_STATUS_DELETE = "ConsoleData.Status.Delete";
  public static final String PROPERTY_STATUS_NEW_DATA = "ConsoleData.Status.NewData";
  public static final String PROPERTY_STATUS_ONLINE = "ConsoleData.Status.Online";
  public static final String PROPERTY_STATUS_PRIVATE = "ConsoleData.Status.Private";
  public static final String PROPERTY_STATUS_VIRTUAL = "ConsoleData.Status.Virtual";
  public static final String PROPERTY_STATUS_ALLOWLOGIN = "ConsoleData.Status.AllowLogin";
  public static final String PROPERTY_STATUS_FORCELOGIN = "ConsoleData.Status.ForceLogin";
  public static final String PROPERTY_STATUS_LOSFRAME = "ConsoleData.Status.LOSFrame";
  public static final String PROPERTY_STATUS_ALLOWSCAN = "ConsoleData.Status.AllowScan";
  public static final String PROPERTY_STATUS_FORCESCAN = "ConsoleData.Status.ForceScan";
  public static final String PROPERTY_STATUS_MULTICONTROLACTIVE =
      "ConsoleData.Status.MultiControlActive";
  public static final String PROPERTY_STATUS_MULTICONTROLMASTER =
      "ConsoleData.Status.MultiControlMaster";
  public static final String PROPERTY_STATUS_MULTICONTROLFRAME =
      "ConsoleData.Status.MultiControlFrame";
  public static final String PROPERTY_STATUS_PORTMODE = "ConsoleData.Status.PortMode";
  public static final String PROPERTY_STATUS_REDUNDANT = "ConsoleData.Status.Redundant";
  public static final String PROPERTY_STATUS_FORCEMACRO = "ConsoleData.Status.ForceMacro";
  public static final String PROPERTY_STATUS_OSDDISABLED = "ConsoleData.Status.OsdDisabled";
  public static final String PROPERTY_STATUS_SUSPENDENABLE = "ConsoleData.Status.SuspendEnable";
  public static final String FIELD_CPU = "CPU";
  public static final String PROPERTY_CPU = "ConsoleData.CPU";
  public static final String FIELD_RDCPU = "RDCPU";
  public static final String PROPERTY_RDCPU = "ConsoleData.RDCPU";
  public static final String FIELD_CONSOLE_VIRTUAL = "ConsoleVirtual";
  public static final String PROPERTY_CONSOLE_VIRTUAL = "ConsoleData.ConsoleVirtual";
  public static final String FIELD_SCAN_TIME = "ScanTime";
  public static final String PROPERTY_SCAN_TIME = "ConsoleData.ScanTime";
  public static final String FIELD_ID = "ID";
  public static final String PROPERTY_ID = "ConsoleData.ID";
  public static final String FIELD_PORTS = "Ports";
  public static final String PROPERTY_PORTS = "ConsoleData.Ports";
  public static final String FIELD_EXTENDER = "Extender";
  public static final String PROPERTY_EXTENDER = "ConsoleData.Extender";
  public static final String FIELD_CPUINDEX = "CpuIndex";
  public static final String PROPERTY_CPUINDEX = "ConsoleData.CpuIndex";
  public static final String FIELD_MSC = "Msc";
  public static final String FIELD_USER = "User";
  public static final String FIELD_RESERVED = "Reserved";
  public static final String PROPERTY_MSC = "ConsoleData.Msc";
  public static final String PROPERTY_MULTSCREEN_INDEX = PROPERTY_BASE + "MultiScreenIndex";
  public static final String PROPERTY_OSD_TRANSPARENCY = PROPERTY_BASE + "OsdTransparency";
  public static final String PROPERTY_SUSPEND_POSITION = PROPERTY_BASE + "SuspendPosition";
  public static final String PROPERTY_SUSPEND_TIME = PROPERTY_BASE + "SuspendTime";
  public static final String PROPERTY_MULTIVIEW_INDEX = PROPERTY_BASE + "SuspendTime";
  public static final String PROPERTY_MOUSE_SPEED = PROPERTY_BASE + "MouseSpeed";
  public static final String PROPERTY_OSD_STYLE = PROPERTY_BASE + "OsdStyle";
  public static final String PROPERTY_CPU_HDMI = PROPERTY_BASE + "CpuHdmi";
  public static final String PROPERTY_USER = "ConsoleData.User";
  public static final String FIELD_VIDEO_ACCESS = "VideoAccess";
  public static final String PROPERTY_VIDEO_ACCESS = "ConsoleData.VideoAccess";
  public static final String FIELD_NO_ACCESS = "NoAccess";
  public static final String PROPERTY_NO_ACCESS = "ConsoleData.NoAccess";
  public static final String FIELD_FAVORITE = "Favorite";
  public static final Map<String, Collection<BitFieldEntry>> BIT_FIELD_MAP;

  private boolean init = false;

  static {
    Collection<BitFieldEntry> bitMapStatus = new HashSet<>();

    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_ACTIVE,
        CaesarConstants.Console.Status.ACTIVE));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_DELETE,
        CaesarConstants.Console.Status.DELETE));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_NEW_DATA,
        CaesarConstants.Console.Status.NEW));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_PRIVATE,
        CaesarConstants.Console.Status.PRIVATE_MODE));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_VIRTUAL,
        CaesarConstants.Console.Status.VIRTUAL_DEVICE));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_ALLOWLOGIN,
        CaesarConstants.Console.Status.ALLOW_USER));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_FORCELOGIN,
        CaesarConstants.Console.Status.FORCE_LOGIN));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_LOSFRAME,
        CaesarConstants.Console.Status.LOS_FRAME));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_PORTMODE,
        CaesarConstants.Console.Status.PORT_MODE));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_REDUNDANT,
        CaesarConstants.Console.Status.REDUNDANT_OFF));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_FORCEMACRO,
        CaesarConstants.Console.Status.FORCE_MACRO));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_OSDDISABLED,
        CaesarConstants.Console.Status.OSD_DISABLED));
    bitMapStatus.add(new BitFieldEntry(ConsoleData.PROPERTY_STATUS_SUSPENDENABLE,
        CaesarConstants.Console.Status.SUSPEND_ENABLE));

    Map<String, Collection<BitFieldEntry>> bitMaps = new HashMap<>();

    bitMaps.put(ConsoleData.PROPERTY_STATUS, Collections.unmodifiableCollection(bitMapStatus));

    BIT_FIELD_MAP = Collections.unmodifiableMap(bitMaps);
  }

  @Expose
  private StringProperty nameProperty = new SimpleStringProperty("");
  @Expose
  private String name;
  @Getter
  @Expose
  private BooleanProperty allowLoginProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private BooleanProperty forceLoginProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private BooleanProperty losFrameProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private BooleanProperty allowScanProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private BooleanProperty forceScanProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private BooleanProperty suspendEnableProperty = new SimpleBooleanProperty(false);
  @Expose
  private IntegerProperty statusProperty = new SimpleIntegerProperty(0); // int status = 0; // con状态
  @Getter
  private int status = 0;
  @Getter
  @Expose
  private int cpu = 0; // 绑定的cpu的oid + 1
  @Getter
  @Expose
  private int rdCpu = 0; // 绑定的redundant cpu的oid + 1
  @Getter
  @Expose
  private int consoleVirtual = 0; // 绑定的virtual con的 oid + 1
  @Getter
  @Expose
  private IntegerProperty scanTimeProperty = new SimpleIntegerProperty(0); // 轮询CPU时的间隔,单位为秒
  @Getter
  @Expose
  private int scanTime;
  @Getter
  @Expose
  private IntegerProperty idProperty = new SimpleIntegerProperty(0); // 设备id
  @Expose
  private int id = 0;
  @Getter
  @Expose
  private int ports = 0; // 关联的extender的个数
  @Expose
  private final int[] extender = createArrayInt(CaesarConstants.Extender.CPUCON);
  /**
   * multiscreen control的配置 msc[0] bit0 CONTROLGROUP.Arrangement.id 如果是手动切屏，设置为0xffffffff msc[1]
   * CONTROLGROUP.Owner.id msc[2] Frame 提示当前键鼠操作的显示器的时间，单位为10ms
   */
  @Expose
  private final int[] msc = createArrayInt(3);
  @Getter
  @Expose
  private int user;
  @Expose
  @Getter
  private int multiScreenIndex = 0;   //跨屏数据的oid+1

  @Getter
  @Expose
  private int osdTransparency = 0;
  @Getter
  @Expose
  private IntegerProperty osdTransparencyProperty = new SimpleIntegerProperty(0);

  @Getter
  @Expose
  private SuspendPosition suspendPosition = SuspendPosition.TOPLEFT;
  @Getter
  @Expose
  private ObjectProperty<SuspendPosition> suspendPositionProperty =
      new SimpleObjectProperty<>(SuspendPosition.TOPLEFT);

  @Getter
  @Expose
  private int suspendTime = 0;
  @Getter
  @Expose
  private IntegerProperty suspendTimeProperty = new SimpleIntegerProperty(0);

  @Getter
  @Expose
  private int mouseSpeed = 10; // 跨屏鼠标速度(取值范围4-16)
  @Getter
  @Expose
  private IntegerProperty mouseSpeedProperty = new SimpleIntegerProperty(10);

  @Getter
  @Expose
  private int osdStyle = 0; // OSD样式,默认为0
  @Getter
  @Expose
  private IntegerProperty osdStyleProperty = new SimpleIntegerProperty();

  @Getter
  @Expose
  private DhdmiSelection cpuHdmi = DhdmiSelection.HDMI1; // 连接CPU哪一路HDMI,0:HDMI1,1:HDMI2
  @Expose
  private final ObjectProperty<DhdmiSelection> cpuHdmiProperty =
      new SimpleObjectProperty<>(DhdmiSelection.HDMI1);

  @Getter
  @Expose
  private int multiviewIndex;
  @Getter
  @Setter
  private int multiviewChannel; // 通道大于0时有效

  @Expose
  private final transient AdvancedBitSet videoAccess; // 如果对某个cpu的权限是videoaccess，那么cpu.oid对应的位为1
  @Expose
  private final transient AdvancedBitSet noAccess; // 如果对某个cpu的权限是noaccess，那么cpu.oid对应的位为1
  @Expose
  private final int[] favorite = createArrayInt(CaesarConstants.Cpu.FAVORITE);

  // favorite的cpu的oid+1的列表

  /**
   * .
   */
  public ConsoleData(CustomPropertyChangeSupport pcs, final ConfigDataManager configDataManager,
      int oid, String fqn) {
    super(pcs, configDataManager, oid, fqn);
    initCommitRollback();

    this.videoAccess = new AdvancedBitSet(configDataManager.getConfigMetaData().getCpuCount());
    this.noAccess = new AdvancedBitSet(configDataManager.getConfigMetaData().getCpuCount());
    for (int i = 0; i < configDataManager.getConfigMetaData().getCpuCount(); i++) {
      this.noAccess.set(i, true);
    }
    this.videoAccess.addPropertyChangeListener(AdvancedBitSet.PROPERTY_BIT, evt -> {
      if (this.init) {
        return;
      }
      if (evt instanceof IndexedPropertyChangeEvent) {
        this.firePropertyChange(AccessControlObject.PROPERTY_VIDEO_ACCESS, evt.getOldValue(),
                evt.getNewValue(), ((IndexedPropertyChangeEvent) evt).getIndex());
      }
    });
    this.noAccess.addPropertyChangeListener(AdvancedBitSet.PROPERTY_BIT, evt -> {
      if (this.init) {
        return;
      }
      if (evt instanceof IndexedPropertyChangeEvent) {
        this.firePropertyChange(AccessControlObject.PROPERTY_NO_ACCESS, evt.getOldValue(),
                evt.getNewValue(), ((IndexedPropertyChangeEvent) evt).getIndex());
      }
    });
  }

  /**
   * 设置属性.
   *
   * @param property 属性名称.
   * @param value    属性值
   */
  public void setProperty(String property, Object value) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (property.equals(PROPERTY_NAME) && value instanceof String) {
      setName((String) value);
    } else if (property.equals(PROPERTY_STATUS_ALLOWLOGIN) && value instanceof Boolean) {
      setStatusAllowLogin((Boolean) value);
    } else if (property.equals(PROPERTY_STATUS_FORCELOGIN) && value instanceof Boolean) {
      setStatusForceLogin((Boolean) value);
    } else if (property.equals(PROPERTY_STATUS_LOSFRAME) && value instanceof Boolean) {
      setStatusLosFrame((Boolean) value);
    } else if (property.equals(PROPERTY_STATUS_ALLOWSCAN) && value instanceof Boolean) {
      setStatusAllowScan((Boolean) value);
    } else if (property.equals(PROPERTY_STATUS_FORCESCAN) && value instanceof Boolean) {
      setStatusForceScan((Boolean) value);
    } else if (property.equals(PROPERTY_STATUS_VIRTUAL) && value instanceof Boolean) {
      setStatusVirtual((Boolean) value);
    } else if (property.equals(PROPERTY_SCAN_TIME) && value instanceof Number) {
      setScanTime(((Number) value).intValue());
    } else if (property.equals(ExtenderData.PROPERTY_NAME) && value instanceof String) {
      if (getExtenderData(0) != null) {
        getExtenderData(0).setName((String) value);
      }
    } else if (property.equals(PROPERTY_ID) && value instanceof Number) {
      setId(((Number) value).intValue());
    } else if (property.equals(PROPERTY_OSD_TRANSPARENCY) && value instanceof Number) {
      setOsdTransparency(((Number) value).intValue());
    } else if (property.equals(PROPERTY_STATUS_SUSPENDENABLE) && value instanceof Boolean) {
      setStatusSuspendEnable((Boolean) value);
    } else if (property.equals(PROPERTY_SUSPEND_POSITION) && value instanceof SuspendPosition) {
      setSuspendPosition((SuspendPosition) value);
    } else if (property.equals(PROPERTY_SUSPEND_TIME) && value instanceof Number) {
      setSuspendTime(((Number) value).intValue());
    } else if (property.equals(PROPERTY_MOUSE_SPEED) && value instanceof Number) {
      setMouseSpeed(((Number) value).intValue());
    } else if (property.equals(PROPERTY_CPU_HDMI) && value instanceof DhdmiSelection) {
      setCpuHdmi((DhdmiSelection) value);
    }
  }

  @Override
  public void initDefaults() {
    super.initDefaults();

    this.init = true;
    for (int i = 0; i < this.msc.length; i++) {
      setMsc(i, 0);
    }
    setUser(0);
    setCpuData(null);
    setRdCpuData(null);
    for (int i = 0; i < this.extender.length; i++) {
      setExtenderData(i, null);
    }
    setScanTime(5);
    setId(0);

    this.videoAccess.clear();
    this.noAccess.clear();
    for (int i = 0; i < getConfigDataManager().getConfigMetaData().getCpuCount(); i++) {
      this.noAccess.set(i, true);
    }
    for (int i = 0; i < this.favorite.length; i++) {
      setFavoriteData(i, null);
    }
    setName("");
    setPorts(0);
    setStatus(0);

    this.init = false;
  }

  public void setInitMode(boolean initMode) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    this.init = initMode;
  }

  public boolean isInitMode() {
    return this.init;
  }

  /**
   * .
   */
  public void setRdCpu(int rcpu) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.rdCpu;
    this.rdCpu = rcpu;
    firePropertyChange(ConsoleData.PROPERTY_RDCPU, oldValue, this.rdCpu);
  }

  /**
   * .
   */
  public void setConsoleVirtual(int consoleVirtual) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.consoleVirtual;
    this.consoleVirtual = consoleVirtual;
    firePropertyChange(ConsoleData.PROPERTY_CONSOLE_VIRTUAL, oldValue, this.consoleVirtual);
  }

  public int getMsc(int idx) {
    return this.msc[idx];
  }

  /**
   * .
   */
  public void setMsc(int idx, int msc) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.msc[idx];
    this.msc[idx] = msc;
    firePropertyChange(ConsoleData.PROPERTY_MSC, oldValue, this.msc[idx], idx);
  }

  /**
   * .
   */
  public void setUser(int user) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.user;
    this.user = user;
    firePropertyChange(ConsoleData.PROPERTY_USER, oldValue, this.user);
  }

  /**
   * .
   */
  public void setMultiScreenIndex(int multiScreenIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.multiScreenIndex;
    this.multiScreenIndex = multiScreenIndex;
    firePropertyChange(ConsoleData.PROPERTY_MULTSCREEN_INDEX, oldValue, this.multiScreenIndex);
  }

  /**
   * .
   */
  public void setOsdTransparency(int osdTransparency) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.osdTransparency;
    if (oldValue != osdTransparency) {
      this.osdTransparency = osdTransparency;
      PlatformUtility.runInFxThread(() -> osdTransparencyProperty.set(osdTransparency));
      firePropertyChange(ConsoleData.PROPERTY_OSD_TRANSPARENCY, oldValue, osdTransparency);
    }
  }

  /**
   * .
   */
  public void setSuspendPosition(SuspendPosition position) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    SuspendPosition oldValue = this.suspendPosition;
    if (oldValue != position) {
      this.suspendPosition = position;
      PlatformUtility.runInFxThread(() -> suspendPositionProperty.set(position));
      firePropertyChange(ConsoleData.PROPERTY_SUSPEND_POSITION, oldValue, position);
    }

  }

  /**
   * .
   */
  public void setSuspendTime(int time) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.suspendTime;
    if (oldValue != time) {
      this.suspendTime = time;
      PlatformUtility.runInFxThread(() -> suspendTimeProperty.set(time));
      firePropertyChange(ConsoleData.PROPERTY_SUSPEND_TIME, oldValue, time);
    }
  }

  /**
   * 设置鼠标速度.
   *
   * @param mouseSpeed 鼠标速度
   */
  public void setMouseSpeed(int mouseSpeed) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.mouseSpeed;
    if (oldValue != mouseSpeed) {
      this.mouseSpeed = mouseSpeed;
      PlatformUtility.runInFxThread(() -> mouseSpeedProperty.set(mouseSpeed));
      firePropertyChange(ConsoleData.PROPERTY_MOUSE_SPEED, oldValue, mouseSpeed);
    }
  }

  /**
   * 设置osd样式.
   *
   * @param osdStyle 样式
   */
  public void setOsdStyle(int osdStyle) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.osdStyle;
    if (oldValue != osdStyle) {
      this.osdStyle = osdStyle;
      PlatformUtility.runInFxThread(() -> osdStyleProperty.set(osdStyle));
      firePropertyChange(ConsoleData.PROPERTY_OSD_STYLE, oldValue, osdStyle);
    }
  }

  public ObjectProperty<DhdmiSelection> cpuHdmiProperty() {
    return cpuHdmiProperty;
  }

  /**
   * 设置连接CPU的HDMI.
   *
   * @param cpuHdmi HDMI
   */
  public void setCpuHdmi(DhdmiSelection cpuHdmi) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    DhdmiSelection oldValue = this.cpuHdmi;
    if (oldValue != cpuHdmi) {
      this.cpuHdmi = cpuHdmi;
      PlatformUtility.runInFxThread(() -> cpuHdmiProperty.set(cpuHdmi));
      firePropertyChange(ConsoleData.PROPERTY_CPU_HDMI, oldValue, cpuHdmi);
    }
  }

  /**
   * .
   */
  public void setMultiviewIndex(int multiviewIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.multiviewIndex;
    if (oldValue != multiviewIndex) {
      this.multiviewIndex = multiviewIndex;
      //PlatformUtility.runInFxThread(() -> multiviewIndexProperty.set(multiviewIndex));
      firePropertyChange(ConsoleData.PROPERTY_MULTIVIEW_INDEX, oldValue, multiviewIndex);
    }
  }

  /**
   * .
   */
  public void setCpu(int cpu) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.cpu;
    this.cpu = cpu;
    firePropertyChange(ConsoleData.PROPERTY_CPU, oldValue, this.cpu);
  }

  public int getCpuIndex(int idx) {
    return this.extender[idx] >> 16;
  }

  /**
   * .
   */
  public void setCpuIndex(int idx, int cpuIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.extender[idx] & 0xFFFF0000;
    this.extender[idx] &= 0xFFFF;
    this.extender[idx] |= cpuIndex << 16;
    firePropertyChange(ConsoleData.PROPERTY_CPUINDEX, oldValue, cpuIndex, idx);
  }

  public int getExtender(int idx) {
    return this.extender[idx] & 0xFFFF;
  }

  /**
   * .
   */
  public void setExtender(int idx, int extender) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.extender[idx] & 0xFFFF;
    this.extender[idx] &= 0xFFFF0000;
    this.extender[idx] |= extender;
    firePropertyChange(ConsoleData.PROPERTY_EXTENDER, oldValue, extender, idx);
  }

  /**
   * .
   */
  public void setScanTime(int scanTime) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.scanTime;
    this.scanTime = scanTime;
    PlatformUtility.runInFxThread(() -> this.scanTimeProperty.set(scanTime));
    firePropertyChange(ConsoleData.PROPERTY_SCAN_TIME, oldValue, scanTime);
  }

  @Override
  public int getId() {
    return this.id;
  }

  /**
   * .
   */
  public void setId(int id) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldVal = id;
    this.id = id;
    PlatformUtility.runInFxThread(() -> this.idProperty.set(id));
    firePropertyChange(PROPERTY_ID, oldVal, id);
  }

  /**
   * .
   */
  public void setVideoAccessCpus(Collection<CpuData> cpuDatas) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuDatas) {
      this.videoAccess.setBits();
    } else {
      int[] bitIndex = new int[cpuDatas.size()];
      CpuData[] cds = cpuDatas.toArray(new CpuData[0]);
      for (int i = 0; i < bitIndex.length; i++) {
        bitIndex[i] = cds[i].getOid();
      }
      this.videoAccess.setBits(bitIndex);
    }
  }

  /**
   * .
   */
  public void setNoAccessCpus(Collection<CpuData> cpuDatas) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuDatas) {
      this.noAccess.setBits();
    } else {
      int[] bitIndex = new int[cpuDatas.size()];
      CpuData[] cds = cpuDatas.toArray(new CpuData[0]);
      for (int i = 0; i < bitIndex.length; i++) {
        bitIndex[i] = cds[i].getOid();
      }
      this.noAccess.setBits(bitIndex);
    }
  }

  @Override
  public int getFavorite(int idx) {
    return this.favorite[idx];
  }

  @Override
  public void setFavorite(int idx, int favorite) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.favorite[idx];
    this.favorite[idx] = favorite;
    firePropertyChange("FavoriteObject.Favorite", oldValue, this.favorite[idx], idx);
  }

  @Override
  public String getName() {
    return this.name;
  }

  /**
   * .
   */
  public void setName(String name) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldVal = this.name;
    this.name = name;
    PlatformUtility.runInFxThread(() -> this.nameProperty.set(name));
    firePropertyChange(PROPERTY_NAME, oldVal, name);
  }

  @Override
  public StringProperty getNameProperty() {
    return this.nameProperty;
  }


  /**
   * .
   */
  public void setPorts(int ports) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.ports;
    this.ports = ports;
    firePropertyChange(ConsoleData.PROPERTY_PORTS, oldValue, this.ports);
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    final int oldValue = this.status;
    this.status = status;
    PlatformUtility.runInFxThread(() -> {
      this.statusProperty.set(status);
      this.allowLoginProperty.set(isStatusAllowLogin());
      this.forceLoginProperty.set(isStatusForceLogin());
      this.losFrameProperty.set(isStatusLosFrame());
      this.allowScanProperty.set(isStatusAllowScan());
      this.forceScanProperty.set(isStatusForceScan());
      this.suspendEnableProperty.set(isStatusSuspendEnable());
    });
    firePropertyChange(ConsoleData.PROPERTY_STATUS, oldValue, status);
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.ACTIVE);
  }

  public boolean isStatusDelete() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.DELETE);
  }

  public boolean isStatusNewData() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.NEW);
  }

  public boolean isStatusPrivate() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.PRIVATE_MODE);
  }

  public boolean isStatusVideoOnly() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.VIDEO_MODE);
  }

  public boolean isStatusVirtual() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.VIRTUAL_DEVICE);
  }

  public boolean isStatusAllowLogin() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.ALLOW_USER);
  }

  public boolean isStatusForceLogin() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.FORCE_LOGIN);
  }

  public boolean isStatusLosFrame() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.LOS_FRAME);
  }

  public boolean isStatusAllowScan() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.ALLOW_CPU_SCAN);
  }

  public boolean isStatusForceScan() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.FORCE_CPU_SCAN);
  }

  public boolean isStatusMultiControlActive() {
    return false;
  }

  public boolean isStatusMultiControlMaster() {
    return false;
  }

  public boolean isStatusPortMode() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.PORT_MODE);
  }

  public boolean isStatusRedundancy() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.REDUNDANT_OFF);
  }

  public boolean isStatusForceMacro() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.FORCE_MACRO);
  }

  public boolean isStatusOsdDisabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.OSD_DISABLED);
  }

  public boolean isStatusSuspendEnable() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.SUSPEND_ENABLE);
  }

  public boolean isStatusVpcon() {
    return isStatusVp7() || isStatusVp6() || isStatusVp7();
  }

  public boolean isStatusVp7() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.VP7_DEVICE);
  }

  public boolean isStatusVp6() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Console.Status.VP6_DEVICE);
  }

  public void setStatusActive(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.ACTIVE));
  }

  public void setStatusDelete(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.DELETE));
  }

  public void setStatusNewData(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.NEW));
  }

  public void setStatusPrivate(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.PRIVATE_MODE));
  }

  public void setStatusVideoOnly(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.VIDEO_MODE));
  }

  public void setStatusVirtual(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.VIRTUAL_DEVICE));
  }

  public void setStatusAllowLogin(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.ALLOW_USER));
  }

  public void setStatusForceLogin(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.FORCE_LOGIN));
  }

  public void setStatusLosFrame(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.LOS_FRAME));
  }

  public void setStatusAllowScan(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.ALLOW_CPU_SCAN));
  }

  public void setStatusForceScan(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.FORCE_CPU_SCAN));
  }

  public void setStatusMultiControlActive(boolean enabled) {
  }

  public void setStatusMultiControlMaster(boolean enabled) {
  }

  public void setStatusPortMode(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.PORT_MODE));
  }

  public void setStatusRedundancy(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.REDUNDANT_OFF));
  }

  public void setStatusForceMacro(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.FORCE_MACRO));
  }

  public void setStatusOsdDisabled(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.OSD_DISABLED));
  }

  public void setStatusSuspendEnable(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, CaesarConstants.Console.Status.SUSPEND_ENABLE));
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (ConsoleData.PROPERTY_NAME.equals(propertyName)) {
      setName((String) value);
    } else if (ConsoleData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus((Integer) value);
    } else if (ConsoleData.PROPERTY_CPU.equals(propertyName)) {
      setCpu((Integer) value);
    } else if (ConsoleData.PROPERTY_RDCPU.equals(propertyName)) {
      setRdCpu((Integer) value);
    } else if (ConsoleData.PROPERTY_CONSOLE_VIRTUAL.equals(propertyName)) {
      setConsoleVirtual((Integer) value);
    } else if (ConsoleData.PROPERTY_SCAN_TIME.equals(propertyName)) {
      setScanTime((Integer) value);
    } else if (ConsoleData.PROPERTY_ID.equals(propertyName)) {
      setId((Integer) value);
    } else if (ConsoleData.PROPERTY_PORTS.equals(propertyName)) {
      setPorts((Integer) value);
    } else if (ConsoleData.PROPERTY_EXTENDER.equals(propertyName)) {
      setExtender(indizes[0], (Integer) value);
    } else if (ConsoleData.PROPERTY_CPUINDEX.equals(propertyName)) {
      setCpuIndex(indizes[0], (Integer) value);
    } else if (ConsoleData.PROPERTY_MSC.equals(propertyName)) {
      setMsc(indizes[0], (Integer) value);
    } else if (ConsoleData.PROPERTY_USER.equals(propertyName)) {
      setUser((Integer) value);
    } else if (ConsoleData.PROPERTY_VIDEO_ACCESS.equals(propertyName)) {
      setVideoAccess(getConfigDataManager().getCpuData(indizes[0]), Boolean.TRUE.equals(value));
    } else if (ConsoleData.PROPERTY_NO_ACCESS.equals(propertyName)) {
      setNoAccess(getConfigDataManager().getCpuData(indizes[0]), Boolean.TRUE.equals(value));
    } else if ("FavoriteObject.Favorite".equals(propertyName)) {
      setFavorite(indizes[0], (Integer) value);
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<ConsoleData> converter =
        getConfigDataManager().getConfigMetaData().getUtilVersion()
            .getDataConverter(ConsoleData.class);
    converter.writeData(this, cfgWriter);
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<ConsoleData> converter =
        getConfigDataManager().getConfigMetaData().getUtilVersion()
            .getDataConverter(ConsoleData.class);
    converter.readData(this, cfgReader);
  }

  /**
   * .
   */
  public Collection<ExtenderData> getOnlineExtenders() {
    Collection<ExtenderData> onlineExtenders = new ArrayList<>();
    for (ExtenderData extenderData : getExtenderDatas()) {
      if (null != extenderData && Utilities.areBitsSet(extenderData.getStatus(),
          CaesarConstants.Extender.Status.ONLINE)) {
        onlineExtenders.add(extenderData);
      }
    }
    return onlineExtenders;
  }

  public boolean isAvailable() {
    return 0 != getPorts() && isStatusActive() && !isStatusVirtual();
  }

  public boolean isReal() {
    return !isStatusVirtual();
  }

  public boolean isVirtual() {
    return isStatusVirtual();
  }

  @Override
  public boolean isVideoAccess(CpuData cpuData) {
    //return null == cpuData ? false : this.videoAccess.get(cpuData.getOId());
    return !(null == cpuData) && this.videoAccess.get(cpuData.getOid());
  }

  @Override
  public void setVideoAccess(CpuData cpuData, boolean locked) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.videoAccess.get(cpuData.getOid());
    this.videoAccess.set(cpuData.getOid(), locked);
    firePropertyChange(ConsoleData.PROPERTY_VIDEO_ACCESS, oldValue, locked, cpuData.getOid());
  }

  @Override
  public Collection<CpuData> getVideoAccessCpuDatas() {
    return getConfigDataManager().getCpuData(this.videoAccess.getBits());
  }

  @Override
  public boolean isNoAccess(CpuData cpuData) {
    //return null == cpuData ? false : this.noAccess.get(cpuData.getOId());
    return null != cpuData && this.noAccess.get(cpuData.getOid());
  }

  @Override
  public void setUsbNoAccess(CpuData paramCpuData, boolean paramBoolean) {
  }

  @Override
  public boolean isUsbNoAccess(CpuData paramCpuData) {
    return false;
  }

  @Override
  public void setNoAccess(CpuData cpuData, boolean locked) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.noAccess.get(cpuData.getOid());
    this.noAccess.set(cpuData.getOid(), locked);
    firePropertyChange(ConsoleData.PROPERTY_NO_ACCESS, oldValue, locked, cpuData.getOid());
  }

  @Override
  public Collection<CpuData> getNoAccessCpuDatas() {
    return getConfigDataManager().getCpuData(this.noAccess.getBits());
  }

  public CpuData getCpuData() {
    return getConfigDataManager().getCpuData(this.cpu - 1);
  }

  /**
   * 获取关联的Multivew数据.
   */
  public MultiviewData getMultiviewData() {
    if (!isMultiview()) {
      return null;
    }
    MultiviewData multiviewData = getConfigDataManager().getMultiviewData(this.multiviewIndex - 1);
    if (multiviewData != null && multiviewData.getConsoleData() != null && multiviewData.getConsoleData().equals(this)) {
      return multiviewData;
    } else {
      return null;
    }
  }


  public void setCpuData(CpuData cpuData) {
    setCpu(null == cpuData ? 0 : cpuData.getOid() + 1);
  }

  public CpuData getRdCpuData() {
    return getConfigDataManager().getCpuData(this.rdCpu - 1);
  }

  public void setRdCpuData(CpuData cpuData) {
    setRdCpu(null == cpuData ? 0 : cpuData.getOid() + 1);
  }

  public ExtenderData getExtenderData(int idx) {
    return getConfigDataManager().getExtenderData(getExtender(idx) - 1);
  }

  /**
   * .
   */
  public Collection<ExtenderData> getExtenderDatas() {
    Collection<ExtenderData> extenderDatas = new ArrayList<>();
    for (int i = 0; i < this.extender.length; i++) {
      ExtenderData extenderData = getExtenderData(i);
      if (null != extenderData) {
        extenderDatas.add(extenderData);
      }
    }
    return extenderDatas;
  }

  public void setExtenderData(int idx, ExtenderData extenderData) {
    setExtender(idx, null == extenderData ? 0 : extenderData.getOid() + 1);
  }

  public boolean isExtenderFull() {
    return getPorts() >= CaesarConstants.Extender.CPUCON;
  }

  /**
   * 是否绑定了指定的外设.
   *
   * @param extenderData 外设数据.
   * @return 如果绑定了，返回true
   */
  public boolean hasExtender(ExtenderData extenderData) {
    for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
      if (getExtenderData(i) == extenderData) {
        return true;
      }
    }
    return false;
  }

  /**
   * 添加绑定的外设.
   *
   * @param extenderData 外设数据
   */
  public void addExtender(ExtenderData extenderData) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (extenderData == null) {
      return;
    }
    if (hasExtender(extenderData)) {
      return;
    }
    for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
      if (getExtenderData(i) == null) {
        setExtenderData(i, extenderData);
        setPorts(getPorts() + 1);
        break;
      }
    }
  }

  /**
   * 删除外设绑定.
   *
   * @param extenderData 要删除的外设
   */
  public void removeExtender(ExtenderData extenderData) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (extenderData == null) {
      return;
    }
    for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
      if (getExtenderData(i) == extenderData) {
        setExtenderData(i, null);
        setPorts(getPorts() - 1);
      }
    }
  }

  @Override
  public CpuData getFavoriteData(int idx) {
    return getConfigDataManager().getCpuData(this.favorite[idx] - 1);
  }

  @Override
  public Collection<CpuData> getFavoriteDatas() {
    Collection<CpuData> cpuDatas = new ArrayList<>();
    for (int i = 0; i < this.favorite.length; i++) {
      CpuData cpuData = getFavoriteData(i);
      if (null != cpuData) {
        cpuDatas.add(cpuData);
      }
    }
    return cpuDatas;
  }

  @Override
  public void setFavoriteData(int idx, CpuData cpuData) {
    setFavorite(idx, null == cpuData ? 0 : cpuData.getOid() + 1);
  }

  /**
   * .
   */
  public void removeFromAssociatedExtenderDatas(boolean commitDependencies, Threshold threshold) {
    for (ExtenderData extenderData : getExtenderDatas()) {
      if (equals(extenderData.getConsoleData())) {
        extenderData.setThreshold(threshold);
        extenderData.setConsoleData(null);
        if (commitDependencies) {
          extenderData.commit(threshold);
        }
        Threshold oldThreshold = extenderData.getThreshold();
        extenderData.setThreshold(oldThreshold);
      } else {
        LOG.log(Level.CONFIG,
            "{0} has {1} attached and it is not configured be attached to the ConsoleData.",
            new Object[]{getFqn(), extenderData.getFqn()});
      }
    }
  }

  @Override
  public void delete(boolean internalCommit) {
    setStatusDelete(true);
    removeFromAssociatedExtenderDatas(internalCommit, getThreshold());
    if (getCpuData() != null) {
      CpuData cpuData = getCpuData();
      if (equals(cpuData.getConsoleData())) {
        Threshold cpuOldThreshold = cpuData.getThreshold();
        cpuData.setThreshold(getThreshold());
        cpuData.setConsoleData(null);
        cpuData.setStatusPrivate(false);
        if (internalCommit) {
          cpuData.commit(getThreshold());

          cpuData.setThreshold(cpuOldThreshold);
        }
      }
    }
    if (getRdCpuData() != null) {
      CpuData cpuData = getRdCpuData();
      if (equals(cpuData.getConsoleData())) {
        Threshold cpuOldThreshold = cpuData.getThreshold();
        cpuData.setThreshold(getThreshold());
        cpuData.setConsoleData(null);
        cpuData.setStatusPrivate(false);
        if (internalCommit) {
          cpuData.commit(getThreshold());

          cpuData.setThreshold(cpuOldThreshold);
        }
      }
    }
    initDefaults();
    if (internalCommit) {
      commit(getThreshold());
    }
  }

  @Override
  public void delete() {
    delete(true);
  }

  @Override
  public AdvancedBitSet getVideoAccessBitSet() {
    return videoAccess;
  }

  @Override
  public void setVideoAccessBits(int... bits) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    getVideoAccessBitSet().setBits(bits);
  }

  @Override
  public AdvancedBitSet getNoAccessBitSet() {
    return noAccess;
  }

  @Override
  public void setNoAccessBits(int... bits) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    getNoAccessBitSet().setBits(bits);
  }

  @Override
  public AdvancedBitSet getUsbNoAccessBitSet() {
    return new AdvancedBitSet();
  }

  @Override
  public void setUsbNoAccessBits(int... bits) {
  }

  @Override
  public int[] getFavoriteArray() {
    return favorite.clone();
  }

  /**
   * 是否在线.
   */
  public boolean isOnline() {
    ExtenderData extenderData = getExtenderData(0);
    return isStatusActive() && extenderData != null
        && (extenderData.getPort() != 0 || extenderData.getRdPort() != 0);
  }

  /**
   * 是否是多画面.
   */
  public boolean isMultiview() {
    return multiviewIndex > 0;
  }

  /**
   * 是否是VP7.
   */
  public boolean isVp7() {
    ExtenderData extenderData = this.getExtenderData(0);
    return extenderData != null && extenderData.getExtenderStatusInfo().getSpecialExtType()
        == CaesarConstants.Extender.SpecialExtenderType.HW_VP7;
  }
}

