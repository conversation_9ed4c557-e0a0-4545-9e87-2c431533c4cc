package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.TxGroupData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarTxGroupPropertySender<T> implements CaesarPropertySender<T> {

  private final CaesarDeviceController controller;
  private final String propertyName;
  private final TxGroupData txGroupData;

  /** Constructor. */
  public CaesarTxGroupPropertySender(
      CaesarDeviceController controller, String propertyName, TxGroupData txGroupData) {
    this.controller = controller;
    this.propertyName = propertyName;
    this.txGroupData = txGroupData;
  }

  @Override
  public void sendValue(T value) {
    controller.execute(
        () -> {
          try {
            txGroupData.setProperty(propertyName, value);
            controller.getDataModel().sendTxGroupData(Collections.singletonList(txGroupData));
          } catch (DeviceConnectionException | BusyException exception) {
            log.error("Fail to send value!", exception);
          }
        });
  }
}
