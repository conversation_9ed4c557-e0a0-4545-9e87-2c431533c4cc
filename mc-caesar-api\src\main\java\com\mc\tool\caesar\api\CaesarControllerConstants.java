package com.mc.tool.caesar.api;

/**
 * .
 *
 * @brief 设备控制所用到的常量的定义
 */
public interface CaesarControllerConstants {

  int BIN_ACK = 0x06;
  int BIN_BSY = 7;
  int BIN_NAK = 0x15;
  int BIN_STX = 2;
  int BIN_ESC = 27;
  int BIN_CMD_RESET = 1;
  int BIN_CMD_RESTART = 2;
  int BIN_CMD_SAVE = 3;
  int BIN_CMD_SAVE_AS = 4;
  int BIN_CMD_ACTIVE = 5;
  int BIN_CMD_SERVICE = 6;
  int BIN_CMD_UPDATE = 7;
  int BIN_CMD_SHUTDOWN = 8;
  int BIN_CMD_SERVICE_MODE_ON = 9;
  int BIN_CMD_SERVICE_MODE_OFF = 10;
  int BIN_CMD_CPU_VIRTUAL_OUT = 9;
  int BIN_CMD_CPU_VIRTUAL_IN = 10;
  int BIN_CPUCON_VIDEO = 1;
  int BIN_CPUCON_PRIVATE = 2;
  int BUFFER_SIZE = 8192;

  byte VERSION_INFO_LEVEL_3 = 0x00;
  byte VPCON_CONFIG_LEVEL_3 = 0x10;
  byte VPCON_RESOLUTION_LEVEL_3 = 0x11;
  byte GET_EXTNAME_LEVEL_3 = 1;

  byte EXT_ARG_TRIGGER_HOLD_TIME = 0x9;

  /**
   * .
   */
  interface Request {

    int getByteValue();

    int getEscapeByte();

    int getServerRequestByte();

    int getServerResponseByte();

    Response isReponse();

    /**
     * 获取request的size字段的长度.
     */
    int getRequestSizeFieldLength();

    /**
     * 获取response的size字段的长度.
     */
    int getResponseSizeFieldLength();

    String name();

    /**
     * .
     */
    enum Response {
      DATA, ACK, NONE;

      Response() {

      }
    }
  }

  /**
   * .
   */
  enum NioPicRequest implements Request {
    UI_USER_LOGIN_RESQUEST(0x51, Response.DATA),
    GET_VERSION(0x60, Response.DATA),  // 获取版本号，还有获取VPCON的配置与分辨率
    SET_UPDATEOPEN(0x61, Response.ACK),
    SET_UPDATEWRITE(0x62, Response.ACK),
    SET_UPDATECLOSE(0x63, Response.ACK),
    SET_RESET(0x64, Response.ACK),
    GET_EXTID(0x65, Response.DATA),
    GET_EXTNAME(0x66, Response.DATA),
    GET_EXTINFO(0x66, Response.DATA),
    GET_MATRIX_MAC(0x67, Response.DATA),
    GET_SERIAL(0x68, Response.DATA),
    SET_GRIDMATRIX_INFO(0x6a, Response.ACK),  // 跟PC软件无关
    SET_VPCON_DATA(0x6b, Response.ACK),
    GET_VPCON_DATA(0x6c, Response.DATA),
    GET_VPCON_RESOLUTION(0x6d, Response.DATA),
    PHY_SET_EXT_VPCON_DATA(0x6e, Response.DATA),
    GET_EXT_VPCON_DATA(0x6f, Response.DATA), // 获取VP6/VP7的配置数据
    SET_FPGA_REGISTER(0x70, Request.Response.ACK),  // 跟PC软件无关
    GET_FPGA_REGISTER(0x71, Response.DATA),  // 跟PC软件无关
    GET_EXT_RESOLUTION(0x75, Response.DATA),
    SET_FPGA_TIMEREGISTER(0x76, Response.ACK),  // 跟PC软件无关
    GET_FPGA_TIMEREGISTER(0x77, Response.DATA), // 跟PC软件无关
    SET_EDID_DATA(0x78, Response.ACK),
    GET_EDID_DATA(0x79, Response.DATA),
    GET_OSD_MD5(0x7f, Response.DATA),
    SET_VPCON_OUTPUT(0x80, Response.ACK),
    GET_VPCON_OUTPUT(0x81, Response.DATA),
    SET_EXT_ARGS(0x84, Response.ACK),
    GET_EXT_ARGS(0x85, Response.DATA),
    SET_VP_SDCARD(0x86, Response.ACK),
    PHY_SET_EXT_NETWORK_INFO(0x8e, Response.DATA), // 设置外设网络配置
    PHY_GET_EXT_NETWORK_INFO(0x8f, Response.DATA), // 获取外设网络配置
    SET_MULTIVIEW_LAYOUT(0x95, Response.ACK),
    SET_MULTIVIEW_OUTPUT_MODE(0x97, Response.ACK),
    SET_VIDEOWALL_RESYNC_VIDEO(0xb3, Response.ACK);

    private int byteValue;
    private CaesarControllerConstants.Request.Response response;

    NioPicRequest(int byteValue, CaesarControllerConstants.Request.Response response) {
      this.byteValue = byteValue;
      this.response = response;
    }

    @Override
    public int getByteValue() {
      return this.byteValue;
    }

    @Override
    public int getEscapeByte() {
      return BIN_ESC;
    }

    @Override
    public int getServerRequestByte() {
      return 0x40;
    }

    @Override
    public int getServerResponseByte() {
      return 0x41;
    }

    @Override
    public CaesarControllerConstants.Request.Response isReponse() {
      return this.response;
    }

    @Override
    public int getRequestSizeFieldLength() {
      return 2;
    }

    @Override
    public int getResponseSizeFieldLength() {
      return 2;
    }
  }

  /**
   * .
   */
  enum SwitchRequest implements Request {
    SET_ALLOFF(0x31, Response.ACK),
    GET_CPU_CONNECT_TO_CON(0x30, Response.DATA),
    SET_CPU_CONNECT_TO_CON(0x32, Response.ACK),
    GET_CPUS_CONNECT_TO_CONS(0x33, Response.DATA),
    SET_CPUS_CONNECT_TO_CONS(0x34, Response.ACK),
    GET_CPU_CONNECT_TO_CON_BIDIRECTIONAL(0x35, Response.DATA),
    GET_CPUS_CONNECT_TO_CONS_BIDIRECTIONAL(0x36, Response.DATA),
    SET_CPUCON_CONNECTION(0x37, Response.ACK),
    SET_CPUCON_CONNECTION_BLOCK(0x38, Response.ACK),
    GET_CPUCON_MATRIX(0x52, Response.DATA),
    SET_CPUCON_CONNECTION_BLOCK_BYMODE(0x53, Response.ACK),
    SET_CPUCON_MATRIX(83, Response.ACK),
    SET_CPUCON_CONNECTION_BYMODE(0x39, Response.ACK),
    GET_ALL_CONNECTION_BIDIRECTIONAL(0x3a, Response.DATA),
    GET_ALL_CONNECTION(0x3b, Response.DATA), //获取所有视频连接关系的CON-CPU组
    GET_CPU_LIST(0x3c, Response.DATA),
    GET_CON_LIST(0x3d, Response.DATA),
    GET_USER_LIST(0x3e, Response.DATA),
    SET_CON_MULTISCREENDATA(0x3f, Response.ACK),
    SET_CON_MULTISCREEN_CMD(0x40, Response.ACK),  // 跟PC软件无关
    SET_IOPORT_STATUS(0x41, Response.ACK),  // 跟PC软件无关
    SET_USERLOGIN(0x42, Response.ACK), // 跟PC软件无关
    SWITCH_POWERPDU_CONTROL(0x5a, Response.ACK),
    SWITCH_MULTIVIEW_TX(0x61, Response.ACK),
    SWITCH_MULTIVIEW_ALL(0x62, Response.ACK),
    SWITCH_MULTIVIEW_ALL_VIDEO(0x65, Response.ACK);

    private int byteValue;
    private Response response;

    SwitchRequest(int byteValue, Response response) {
      this.byteValue = byteValue;
      this.response = response;
    }

    @Override
    public int getByteValue() {
      return this.byteValue;
    }

    @Override
    public int getEscapeByte() {
      return BIN_ESC;
    }

    @Override
    public int getServerRequestByte() {
      return 0x50;
    }

    @Override
    public int getServerResponseByte() {
      return 0x51;
    }

    @Override
    public CaesarControllerConstants.Request.Response isReponse() {
      return this.response;
    }

    @Override
    public int getRequestSizeFieldLength() {
      return 2;
    }

    @Override
    public int getResponseSizeFieldLength() {
      return 2;
    }
  }

  /**
   * .
   */
  enum SystemRequest implements Request {
    GET_CFGDATA(0x10, Response.DATA),
    SET_CFGDATA(0x11, Response.ACK),  // 未被使用
    GET_SYSDATA(0x12, Response.DATA),
    SET_SYSDATA(0x13, Response.ACK),
    GET_PORTDATA(0x14, Response.DATA),
    SET_PORTDATA(0x15, Response.ACK),
    GET_CPUDATA(0x16, Response.DATA),
    SET_CPUDATA(0x17, Response.ACK),
    GET_CONDATA(0x18, Response.DATA),
    SET_CONDATA(0x19, Response.ACK),
    GET_EXTDATA(0x1a, Response.DATA),
    SET_EXTDATA(0x1b, Response.ACK),
    GET_USERDATA(0x1c, Response.DATA),
    SET_USERDATA(0x1d, Response.ACK),
    GET_MODLIST(0x1e, Response.DATA),
    GET_MULTISCREENDATA(0x1f, Response.DATA),
    SET_MULTISCREENDATA(0x20, Response.ACK),
    GET_USERGROUPDATA(0x22, Response.DATA),
    SET_USERGROUPDATA(0x23, Response.ACK),
    GET_TIME(0x24, Response.DATA),
    SET_TIME(0x25, Response.ACK),
    GET_FKEYDATA(89, Response.DATA),
    SET_FKEYDATA(0x26, Response.ACK),
    GET_LICENCE(98, Response.DATA),
    SET_LICENCEKEY(100, Response.DATA),
    SET_MATDATA(102, Response.ACK),
    GET_SYSLOG_START(0x28, Response.ACK),
    GET_SYSLOG_END(0x29, Response.ACK),
    GET_CPUAPP_VERSION(0x2a, Response.DATA),
    SYSTEM_SET_SYSCMD(0x2b, Response.ACK),
    SYSTEM_SETFILE_START(0x2c, Response.ACK),
    SYSTEM_SETFILE_READ(0x2d, Response.DATA),
    SYSTEM_SETFILE_WRITE(0x2e, Response.ACK),
    SYSTEM_SETFILE_END(0x2f, Response.ACK),
    GET_MATRIX_GRIDINFO(0x31, Response.DATA),
    SET_EXT_IMPORT(0x3F, Response.ACK),
    GET_TXGROUP(0x34, Response.DATA),
    SET_TXGROUP(0x35, Response.ACK),
    GET_ACTIVE_TX(0x36, Response.DATA),
    GET_ACTIVE_RX(0x37, Response.DATA),
    GET_ACTIVE_EXT(0x38, Response.DATA),
    GET_ACTIVE_USER(0x39, Response.DATA),
    GET_ACTIVE_USER_GROUP(0x3A, Response.DATA),
    GET_ACTIVE_MULTISCREEN(0x3B, Response.DATA),
    GET_ACTIVE_MACRO(0x3C, Response.DATA),
    GET_ACTIVE_GRID(0x3D, Response.DATA),
    GET_ACTIVE_CPU_GROUP(0x3E, Response.DATA),
    MATRIX_OPPOSITE_REBOOT(0x47, Response.ACK),
    GET_BRANCH_DATA(0x48, Response.DATA),
    SET_BRANCH_DATA(0x49, Response.ACK),
    GET_MULTIVIEW_DATA(0x50, Response.DATA),
    SET_MULTIVIEW_DATA(0x51, Response.ACK),
    GET_SOURCE_CLIP_DATA(0x52, Response.DATA),
    SET_SOURCE_CLIP_DATA(0x53, Response.ACK),
    SET_VPBACKUP_DATA(0xd2, Response.ACK),
    GET_VPBACKUP_DATA(0xd3, Response.DATA),
    SET_SCENARIO_DATA(0xe1, Response.ACK) {
      @Override
      public int getRequestSizeFieldLength() {
        return 4;
      }
    }, GET_SCENARIO_DATA(0xe2, CaesarControllerConstants.Request.Response.DATA) {
      @Override
      public int getResponseSizeFieldLength() {
        return 4;
      }
    },
    DELETE_SCENARIO_DATA(0xe3, CaesarControllerConstants.Request.Response.ACK),
    GET_SCENARIO_LIST(0xe4, CaesarControllerConstants.Request.Response.DATA),
    START_SCENARIO(0xe5, CaesarControllerConstants.Request.Response.ACK),
    BEGIN_UPDATE_VW(0xda, CaesarControllerConstants.Request.Response.ACK),
    END_UPDATE_VW(0xdb, CaesarControllerConstants.Request.Response.ACK),
    SET_USB_DEVICE_BINDING(0x72, CaesarControllerConstants.Request.Response.ACK),
    GET_SFP_INFO(0x73, CaesarControllerConstants.Request.Response.DATA),
    CHECK_USERNAME_VALIDITY(0x74, CaesarControllerConstants.Request.Response.DATA);

    private int byteValue;
    private CaesarControllerConstants.Request.Response response;

    SystemRequest(int byteValue, CaesarControllerConstants.Request.Response response) {
      this.byteValue = byteValue;
      this.response = response;
    }

    @Override
    public int getByteValue() {
      return this.byteValue;
    }

    @Override
    public int getEscapeByte() {
      return BIN_ESC;
    }

    @Override
    public int getServerRequestByte() {
      return 0x30;
    }

    @Override
    public int getServerResponseByte() {
      return 0x31;
    }

    @Override
    public CaesarControllerConstants.Request.Response isReponse() {
      return this.response;
    }

    @Override
    public int getRequestSizeFieldLength() {
      return 2;
    }

    @Override
    public int getResponseSizeFieldLength() {
      return 2;
    }
  }

  /**
   * .
   */
  enum BroadcastRequest implements CaesarControllerConstants.Request {
    SET_GRIDMASTER(0x30, Response.NONE), GET_GRIDINFO(0x55, Response.NONE),
    RET_GRIDINFO(0x55, Response.NONE), GET_MATRIX_ACTIVE(0x56, Response.NONE),
    MATRIX_DOUBLEBACKUP_STATUS(0x57, Response.DATA);

    private int byteValue;
    private CaesarControllerConstants.Request.Response response;

    BroadcastRequest(int byteValue, Response response) {
      this.byteValue = byteValue;
      this.response = response;
    }

    @Override
    public int getByteValue() {
      return this.byteValue;
    }

    @Override
    public int getEscapeByte() {
      return BIN_ESC;
    }

    @Override
    public int getServerRequestByte() {
      return 0x20;
    }

    @Override
    public int getServerResponseByte() {
      return 0x21;
    }

    @Override
    public CaesarControllerConstants.Request.Response isReponse() {
      return this.response;
    }

    @Override
    public int getRequestSizeFieldLength() {
      return 2;
    }

    @Override
    public int getResponseSizeFieldLength() {
      return 2;
    }
  }
}

