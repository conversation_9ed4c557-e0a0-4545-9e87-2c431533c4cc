package com.mc.tool.caesar.vpm.util.vp;

import com.mc.tool.caesar.api.SwitchMultiviewAllVideoChannel;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.vp.ColorUtility;
import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;
import javafx.geometry.Rectangle2D;
import javafx.util.Pair;

/**
 * VP7处理器.
 */
public class Vp7Processor {

  /**
   * 处理结果.
   */
  public enum Status {
    SUCCESS, PARTIAL_SUCCESS // 部分窗口超出范围
  }

  /**
   * 视频墙错误.
   */
  public enum VideoWallError {
    WINDOW_OVERFLOW,
    SCREEN_LAYER_OVERFLOW,
    INVALID_SCREEN
  }

  /**
   * 检查VP7视频墙.
   */
  public static Collection<Pair<Integer, Vp7Processor.VideoWallError>> checkVideoWall(
      VpMatrix matrix, VpVideoWall videoWall) {
    List<Pair<Integer, Vp7Processor.VideoWallError>> errors = new ArrayList<>();
    List<VpVideoDataWrapper> allVideos = recreateVideos(matrix, videoWall, true, true);
    Collection<Collection<Integer>> vpconIds = getVpconIds(matrix, videoWall);
    for (Collection<Integer> ids : vpconIds) {
      if (ids.isEmpty()) {
        continue;
      }
      int vp7Id = getVp7Id(ids.iterator().next());
      // 提取相关视频窗口
      List<VpVideoDataWrapper> videos = groupVideos(videoWall, vp7Id, allVideos);

      // 分配通道
      videos = allocateChannel(matrix, vp7Id, videos);

      // 过滤窗口
      int oldSize = videos.size();
      videos = filterVideos(matrix, vp7Id, videos);
      int newSize = videos.size();
      if (newSize < oldSize) {
        errors.addAll(ids.stream()
            .map((item) -> new Pair<>(item, Vp7Processor.VideoWallError.WINDOW_OVERFLOW))
            .collect(Collectors.toList()));
        continue;
      }

      // 切割图层
      List<VpScreenLayers> layers = splitLayers(videoWall, vp7Id, videos);
      Vp7InfType infType = matrix.getVp7InfType(vp7Id);
      for (VpScreenLayers screenLayers : layers) {
        if (!isScreenValid(infType, getScreenIndex(screenLayers.rxId))) {
          errors.add(new Pair<>(screenLayers.rxId, Vp7Processor.VideoWallError.INVALID_SCREEN));
        } else if (screenLayers.layers.size() > getMaxLayerCountPerScreen(infType)) {
          errors.add(new Pair<>(screenLayers.rxId, Vp7Processor.VideoWallError.SCREEN_LAYER_OVERFLOW));
        }
      }
    }
    return errors;
  }

  /**
   * 处理VP7视频墙.
   */
  public static Status processVideoWall(VpMatrix matrix, VpVideoWall videoWall, Vp7WallCurrCfg currCfg) {

    List<VpVideoDataWrapper> newVideoDatas = recreateVideos(matrix, videoWall, true, true);

    Collection<Collection<Integer>> vpconIds = getVpconIds(matrix, videoWall);

    Status result = Status.SUCCESS;
    for (Collection<Integer> ids : vpconIds) {
      if (ids.isEmpty()) {
        continue;
      }
      int vp7Id = getVp7Id(ids.iterator().next());
      if (!matrix.isRxOnline(vp7Id)) {
        continue;
      }

      Status ret = processOneVpcon(matrix, videoWall, vp7Id, newVideoDatas, new HashSet<>(), currCfg);

      if (ret != Status.SUCCESS) {
        result = ret;
      }
    }
    return result;
  }

  public static void applyVideoWall(VpMatrix matrix, VpVideoWall videoWall, Vp7WallCurrCfg currCfg) {
    // Implementation here
  }

  static List<VpVideoDataWrapper> recreateVideos(VpMatrix matrix, VpVideoWall videoWall, boolean filterEmptyWindow,
                                                 boolean useFinalResolution) {

    List<VpVideoDataWrapper> result = new ArrayList<>();

    // Iterate through all video data
    for (int i = 0; i < videoWall.getVideoCount(); i++) {
      // 获取视频数据，位置大小取偶数
      VpVideoData data = videoWall.getVideoData(i);
      if (data == null) {
        continue;
      }
      data = new VpSimpleVideoData(roundToEven(data.getLeft()), roundToEven(data.getTop()), roundToEven(data.getWidth()),
          roundToEven(data.getHeight()),
          data.getAlpha(), data.getTxId(), data.getName(), data.getSourceIndex(), data.getClipIndex());
      // 获取分辨率，取偶数
      VpResolution resolution = useFinalResolution ? matrix.getTxFinalResolution(data.getTxId(), data.getSourceIndex()) :
          matrix.getTxCurrentResolution(data.getTxId(), data.getSourceIndex());
      resolution = new VpResolution(roundToEven(resolution.width), roundToEven(resolution.height), resolution.type);
      // 获取裁剪数据，取偶数
      VpTxClipData txClipData = matrix.getTxClipData(data.getTxId(), data.getSourceIndex(), data.getClipIndex());
      txClipData = new VpTxClipData(roundToEven(txClipData.getLeft()), roundToEven(txClipData.getTop()),
            roundToEven(txClipData.getRight()), roundToEven(txClipData.getBottom()));
      VpVideoDataWrapper wrapper = new VpVideoDataWrapper(data, resolution, txClipData, i, false);

      if (filterEmptyWindow) {
        // 去掉不在线窗口
        if (data.getTxId() <= 0 || !matrix.isExtOnline(data.getTxId())) {
          continue;
        }

        // 去掉分辨率异常窗口
        if (!wrapper.isValid()) {
          continue;
        }
      }

      // Add the wrapper to the result list
      result.add(wrapper);
    }

    return result;
  }

  static Collection<Collection<Integer>> getVpconIds(VpMatrix matrix, VpVideoWall videoWall) {
    Set<Integer> ids = new HashSet<>();
    for (int i = 0; i < videoWall.getRows(); i++) {
      for (int j = 0; j < videoWall.getColumns(); j++) {
        VpScreenData screenData = videoWall.getScreenData(i, j);
        if (screenData == null || screenData.getRxId() == 0) {
          continue;
        }
        ids.add(screenData.getRxId());
      }
    }

    return new ArrayList<>(
        ids.stream().collect(Collectors.groupingBy(Vp7Processor::getVp7Id)).values());
  }

  static Status processOneVpcon(VpMatrix matrix, VpVideoWall videoWall, int vpconId, List<VpVideoDataWrapper> allVideos,
                                Set<VpVideoPortInfo> oldPortInfos, Vp7WallCurrCfg currCfg) {

    Status status = Status.SUCCESS;

    // 提取相关视频窗口
    List<VpVideoDataWrapper> videos = groupVideos(videoWall, vpconId, allVideos);

    // 分配通道
    videos = allocateChannel(matrix, vpconId, videos);

    // 过滤窗口
    int oldSize = videos.size();
    videos = filterVideos(matrix, vpconId, videos);
    int newSize = videos.size();
    if (newSize < oldSize) {
      status = Status.PARTIAL_SUCCESS;
    }

    // 切割图层
    List<VpScreenLayers> layers = splitLayers(videoWall, vpconId, videos);

    // 过滤图层
    Vp7InfType infType = matrix.getVp7InfType(vpconId);
    oldSize = layers.stream().mapToInt(layer -> layer.layers.size()).sum();
    layers = filterLayers(matrix, layers, infType);
    newSize = layers.stream().mapToInt(layer -> layer.layers.size()).sum();
    if (newSize < oldSize) {
      status = Status.PARTIAL_SUCCESS;
    }

    // 生成配置
    Vp7ConfigData configData = calculateConfig(matrix, videoWall, infType, layers);
    currCfg.setVpConfigData(vpconId, configData);
    // 生成端口配置
    Set<VpVideoPortInfo> newPortInfos = new HashSet<>();
    for (VpVideoDataWrapper video : videos) {
      VpVideoPortInfo portInfo = new VpVideoPortInfo();
      portInfo.portIndex = video.getPort();
      portInfo.txId = video.getTxId();
      portInfo.txIndex = video.getSourceIndex();
      portInfo.left = video.getLeft();
      portInfo.top = video.getTop();
      portInfo.width = video.getWidth();
      portInfo.height = video.getHeight();
      newPortInfos.add(portInfo);
    }
    currCfg.setVpconPortInfos(vpconId, newPortInfos);
    return status;
  }

  static List<VpVideoDataWrapper> groupVideos(VpVideoWall videoWall, int vpconId, List<VpVideoDataWrapper> videoDatas) {

    List<VpVideoDataWrapper> result = new ArrayList<>();
    List<Rectangle2D> rxRects = new ArrayList<>();

    // 计算RX的空间
    for (int i = 0; i < videoWall.getRows(); i++) {
      for (int j = 0; j < videoWall.getColumns(); j++) {
        VpScreenData screenData = videoWall.getScreenData(i, j);
        if (getVp7Id(screenData.getRxId()) == vpconId) {
          VpResolution res = screenData.getResolution();
          int minX = ceilToEven(screenData.getXpos());
          int minY = ceilToEven(screenData.getYpos());
          int maxX = floorToEven(screenData.getXpos() + res.width);
          int maxY = floorToEven(screenData.getYpos() + res.height);
          rxRects.add(new Rectangle2D(minX, minY, maxX - minX, maxY - minY));
        }
      }
    }

    // 筛选与RX空间有交集的Video
    for (VpVideoDataWrapper videoData : videoDatas) {
      Rectangle2D txRect = new Rectangle2D(videoData.getLeft(), videoData.getTop(), videoData.getWidth(), videoData.getHeight());
      for (Rectangle2D rect : rxRects) {
        if (txRect.intersects(rect)) {
          result.add(videoData);
          break; // Exit the inner loop once a match is found
        }
      }
    }

    return result;
  }

  static List<VpVideoDataWrapper> allocateChannel(VpMatrix matrix, int vpconId, List<VpVideoDataWrapper> videos) {

    // 总共8个输入(s1~s8)，16个通道(v1~v16)
    // s1占用v1、v2,s2占用v9、10,s3占用v3、v4，如此类推
    // 如果s1是4K60，使用v1通道，s2无法使用
    // 如果s1是4K30，左半部分使用v1通道，右半部分使用v2通道
    // 如果s1是DHDMI，第一路视频使用v1通道，第二路视频使用v2通道
    // 4K60输入必须在其他输入之前
    // 统计4K60窗口个数

    int countOther = 0;
    int count4k60 = 0;

    // TX, <port, channel>
    Map<Integer, Pair<Integer, Integer>> channelMap = new TreeMap<>();

    for (VpVideoDataWrapper video : videos) {
      if (channelMap.containsKey(video.getTxId())) {
        continue;
      }
      channelMap.put(video.getTxId(), new Pair<>(-1, -1));

      if (video.getResolution().type == VpResolution.Type.VIDEO_4K60) {
        if (((count4k60 + 1) * 2 + countOther) <= Vp7Constants.MAX_VIDEO_INPUT) {
          count4k60++;
        }
      } else {
        if (count4k60 * 2 + countOther + 1 <= Vp7Constants.MAX_VIDEO_INPUT) {
          countOther++;
        }
      }
    }

    // 按顺序分配通道
    int curr4k60Input = 0; // s1~s8 - 1
    int currOtherInput = count4k60 * 2; // s1~s8 - 1

    for (VpVideoDataWrapper video : videos) {
      Pair<Integer, Integer> itr = channelMap.get(video.getTxId());
      if (itr == null) {
        // 不应该找不到的
        continue;
      }

      // 已经分配过通道
      if (itr.getKey() >= 0) {
        video.setPort(itr.getKey());
        video.setChannel(itr.getValue() + video.getSourceIndex());
        continue;
      }

      // 分配新通道
      if (video.getResolution().type == VpResolution.Type.VIDEO_4K60) {
        if (curr4k60Input < count4k60) {
          // 4K60只用使用s1、s3、s5、s7对应的通道v1、v3、v5、v7
          video.setPort(curr4k60Input);
          video.setChannel(curr4k60Input * 2);
          curr4k60Input++;
          channelMap.put(video.getTxId(), new Pair<>(video.getPort(), video.getChannel()));
        }
      } else {
        if (currOtherInput < Vp7Constants.MAX_VIDEO_INPUT) {
          int channel = (currOtherInput % 2) * Vp7Constants.HALF_CHANNEL_COUNT + ((currOtherInput >> 1) << 1);
          video.setPort(currOtherInput - count4k60);
          video.setChannel(channel + video.getSourceIndex());
          channelMap.put(video.getTxId(), new Pair<>(video.getPort(), channel));
          currOtherInput++;
        }
      }
    }

    // 切割4K30信号
    List<VpVideoDataWrapper> result = new ArrayList<>();
    for (VpVideoDataWrapper video : videos) {
      if (video.getResolution().type == VpResolution.Type.VIDEO_4K30) {
        List<VpVideoDataWrapper> splitVideos = split4k30Videos(video);
        result.addAll(splitVideos);
      } else {
        result.add(video);
      }
    }

    return result;
  }

  // 过滤没有端口信息的窗口
  static List<VpVideoDataWrapper> filterVideos(VpMatrix matrix, int vpconId, List<VpVideoDataWrapper> videos) {

    videos.removeIf(video -> video.getPort() < 0);
    return videos;
  }

  // 切割图层
  static List<VpScreenLayers> splitLayers(VpVideoWall videoWall, int vpconId, List<VpVideoDataWrapper> videos) {

    Map<Integer, Rectangle2D> rxRects = new HashMap<>();
    Map<Integer, VpScreenLayers> rxLayers = new TreeMap<>();

    // 计算RX空间
    for (int i = 0; i < videoWall.getRows(); i++) {
      for (int j = 0; j < videoWall.getColumns(); j++) {
        VpScreenData screenData = videoWall.getScreenData(i, j);
        if (getVp7Id(screenData.getRxId()) == vpconId) {
          VpResolution res = screenData.getResolution();
          int minX = ceilToEven(screenData.getXpos());
          int minY = ceilToEven(screenData.getYpos());
          int maxX = floorToEven(screenData.getXpos() + res.width);
          int maxY = floorToEven(screenData.getYpos() + res.height);
          Rectangle2D screenBounds = new Rectangle2D(minX, minY, maxX - minX, maxY - minY);
          rxRects.put(screenData.getRxId(), screenBounds);

          int oid = i * videoWall.getColumns() + j;
          VpScreenLayers layers = new VpScreenLayers();
          layers.rxId = screenData.getRxId();
          layers.oid = oid;
          layers.screenBounds = screenBounds;
          rxLayers.put(screenData.getRxId(), layers);
        }
      }
    }

    // 裁剪图层
    for (VpVideoDataWrapper videoData : videos) {
      Rectangle2D txRect = new Rectangle2D(videoData.getLeft(), videoData.getTop(), videoData.getWidth(), videoData.getHeight());
      for (Map.Entry<Integer, Rectangle2D> rectEntry : rxRects.entrySet()) {
        final int rxId = rectEntry.getKey();
        Rectangle2D rxRect = rectEntry.getValue();
        Rectangle2D intersection = getIntersection(txRect, rxRect);
        if (intersection == null) {
          continue;
        }

        VpClipData clipData = new VpClipData();
        // 输出参数
        clipData.setOutX((int) (intersection.getMinX() - rxRect.getMinX()));
        clipData.setOutY((int) (intersection.getMinY() - rxRect.getMinY()));
        clipData.setOutWidth((int) intersection.getWidth());
        clipData.setOutHeight((int) intersection.getHeight());

        // 计算裁剪参数
        clipData.setScaledWidth(
            (int) (txRect.getWidth() * videoData.getResolution().width / videoData.getClippedResolutionWidth()));
        clipData.setScaledHeight(
            (int) (txRect.getHeight() * videoData.getResolution().height / videoData.getClippedResolutionHeight()));
        clipData.setScaledClipX((int) (intersection.getMinX() - txRect.getMinX()
            + txRect.getWidth() * videoData.getTxClipData().getLeft() / videoData.getClippedResolutionWidth()));
        clipData.setScaledClipY((int) (intersection.getMinY() - txRect.getMinY()
            + txRect.getHeight() * videoData.getTxClipData().getTop() / videoData.getClippedResolutionHeight()));
        clipData.setScaledClipWidth((int) intersection.getWidth());
        clipData.setScaledClipHeight((int) intersection.getHeight());

        clipData.setOriginWidth(videoData.getResolution().width);
        clipData.setOriginHeight(videoData.getResolution().height);
        double scaleW = 1.0 * videoData.getClippedResolutionWidth() / txRect.getWidth();
        double scaleH = 1.0 * videoData.getClippedResolutionHeight() / txRect.getHeight();

        clipData.setOriginClipX(roundToEven(clipData.getScaledClipX() * scaleW));
        clipData.setOriginClipY(roundToEven(clipData.getScaledClipY() * scaleH));
        clipData.setOriginClipWidth(
            roundToEven((clipData.getScaledClipX() + clipData.getScaledClipWidth()) * scaleW) - clipData.getOriginClipX());
        clipData.setOriginClipHeight(
            roundToEven((clipData.getScaledClipY() + clipData.getScaledClipHeight()) * scaleH) - clipData.getOriginClipY());

        clipData.setChannel(videoData.getChannel());
        clipData.setAlpha(videoData.getAlpha());
        clipData.setRxId(rxId);

        // Left seam compensation
        if (Double.compare(intersection.getMinX(), rxRect.getMinX()) == 0  // 图层在屏幕最左侧
            && intersection.getMinX() > 0        // 图层不在大屏最左侧
            && clipData.getOriginClipX() > 0) {  // 图层不在原始视频最左侧
          clipData.setLeftCompensation(Vp7Constants.CLIP_L_PIECE);
        }
        // Right seam compensation
        if (Double.compare(intersection.getMaxX(), rxRect.getMaxX()) == 0  // 图层在屏幕最右侧
            && intersection.getMaxX() < videoWall.getTotalWidth()  // 图层不在大屏最右侧
            && clipData.getOriginClipX() + clipData.getOriginClipWidth() < clipData.getOriginWidth()) {  // 图层不在原始视频最右侧
          clipData.setRightCompensation(Vp7Constants.CLIP_R_PIECE);
        }
        // Top seam compensation
        if (Double.compare(intersection.getMinY(), rxRect.getMinY()) == 0  // 图层在屏幕最上侧
            && intersection.getMinY() > 0        // 图层不在大屏最上侧
            && clipData.getOriginClipY() > 0) {  // 图层不在原始视频最上侧
          clipData.setTopCompensation(Vp7Constants.CLIP_T_PIECE);
        }
        // Bottom seam compensation
        if (Double.compare(intersection.getMaxY(), rxRect.getMaxY()) == 0  // 图层在屏幕最下侧
            && intersection.getMaxY() < videoWall.getTotalHeight()  // 图层不在大屏最下侧
            && clipData.getOriginClipY() + clipData.getOriginClipHeight() < clipData.getOriginHeight()) {  // 图层不在原始视频最下侧
          clipData.setBottomCompensation(Vp7Constants.CLIP_B_PIECE);
        }

        VpScreenLayers layers = rxLayers.get(rxId);
        layers.layers.add(clipData);
      }
    }

    List<VpScreenLayers> result = new ArrayList<>(rxLayers.values());
    return result;
  }

  static List<VpScreenLayers> filterLayers(VpMatrix matrix, List<VpScreenLayers> layers, Vp7InfType infType) {
    if (layers.isEmpty()) {
      return layers;
    }

    // 过滤无效的输出
    layers.removeIf(item -> !isScreenValid(infType, getScreenIndex(item.rxId)));

    // 过滤超出的图层
    int maxLayerCountPerScreen = getMaxLayerCountPerScreen(infType);
    int maxTotalLayerCount = Vp7Constants.MAX_TOTAL_LAYER_COUNT;
    int totalLayerCount = 0;

    for (VpScreenLayers screen : layers) {
      if (totalLayerCount > maxTotalLayerCount) {
        screen.layers.clear();
      }
      int maxCount = Math.min(maxTotalLayerCount - totalLayerCount, maxLayerCountPerScreen);
      if (screen.layers.size() > maxCount) {
        screen.layers.subList(maxCount, screen.layers.size()).clear();
      }
      totalLayerCount += screen.layers.size();
    }

    return layers;
  }

  static Vp7ConfigData calculateConfig(VpMatrix matrix, VpVideoWall videoWall, Vp7InfType infType, List<VpScreenLayers> screens) {
    Vp7ConfigData result = new Vp7ConfigData();
    result.totalWidth.set(videoWall.getTotalWidth());
    result.totalHeight.set(videoWall.getTotalHeight());

    // logo配置
    VpOsdData osd = videoWall.getOsdData();
    result.logoConfig.logoColor.set(ColorUtility.toRgb(osd.getOsdColor()));
    result.logoConfig.bgColor.set(ColorUtility.toRgb(osd.getBgColor()));

    for (VpScreenLayers screen : screens) {
      int idx = getScreenIndex(screen.rxId);
      if (idx >= Vp7Constants.MAX_SCREEN_COUNT) {
        continue;
      }
      result.logoConfig.oid[idx].set(screen.oid);
    }

    result.logoConfig.logoLeft.set(osd.getOsdLeft());
    result.logoConfig.logoTop.set(osd.getOsdTop());
    result.logoConfig.logoWidth.set(osd.getOsdWidth());
    result.logoConfig.logoHeight.set(osd.getOsdHeight());

    // 测试画面配置
    Vp7ConfigData.Vp7TpgConfig tpgConfig = videoWall.createVp7TestFrameData();
    result.tpgConfig.mode.set(tpgConfig.mode.get());
    result.tpgConfig.color.set(tpgConfig.color.get());
    result.tpgConfig.speed.set(tpgConfig.speed.get());
    // 重置优先级
    for (int i = 0; i < Vp7Constants.MAX_SCREEN_COUNT; i++) {
      for (int j = 0; j < Vp7Constants.MAX_PRIORITY_COUNT; j++) {
        result.portConfig[i].layerPriority[j].set(0);
      }
    }
    // 图层配置
    int globalLayerCount = screens.stream().map((item) -> item.layers.size()).reduce(0, Integer::sum);
    Map<Integer, Integer> layerCountPerScreen = screens.stream().collect(Collectors.toMap(
        item -> getScreenIndex(item.rxId),
        item -> item.layers.size()));
    int globalLayerIndex = 0;
    int priority = Vp7Constants.MAX_PRIORITY_COUNT - globalLayerCount + 1;
    for (VpScreenLayers screen : screens) {
      int screenIndex = getScreenIndex(screen.rxId);
      int startVpssIndex = getStartVpssIndexForScreen(infType, screenIndex);
      if (screenIndex < 0 || screenIndex >= Vp7Constants.MAX_SCREEN_COUNT || startVpssIndex < 0) {
        // 异常情况
        continue;
      }
      int layerIndex = 0;
      for (VpClipData layer : screen.layers) {
        int vpssIndex = startVpssIndex + layerIndex;
        if (vpssIndex >= Vp7Constants.MAX_TOTAL_LAYER_COUNT) {
          // 异常情况
          break;
        }

        // VPSS配置
        result.vpssLayerConfig[vpssIndex].vidSelect.set(layer.getChannel() + 1); // layer.channel的值的范围为0~15，vid_select的值的有效范围为1~16

        // 裁剪
        result.vpssLayerConfig[vpssIndex].preClipxOffset.set(layer.getOriginClipX());
        result.vpssLayerConfig[vpssIndex].preClipxWidth.set(layer.getOriginClipWidth());
        result.vpssLayerConfig[vpssIndex].preClipyOffset.set(layer.getOriginClipY());
        result.vpssLayerConfig[vpssIndex].preClipyHeight.set(layer.getOriginClipHeight());

        // 缩小
        result.vpssLayerConfig[vpssIndex].scaleDownWidth.set(Math.min(layer.getScaledClipWidth(), layer.getOriginClipWidth()));
        result.vpssLayerConfig[vpssIndex].scaleDownHeight.set(Math.min(layer.getScaledClipHeight(), layer.getOriginClipHeight()));

        // 放大
        result.vpssLayerConfig[vpssIndex].scaleUpWidth.set(layer.getScaledClipWidth());
        result.vpssLayerConfig[vpssIndex].scaleUpHeight.set(layer.getScaledClipHeight());

        // 布局
        result.vpssLayerConfig[vpssIndex].layerX.set(layer.getOutX());
        result.vpssLayerConfig[vpssIndex].layerY.set(layer.getOutY());
        result.vpssLayerConfig[vpssIndex].layerWidth.set(layer.getOutWidth());
        result.vpssLayerConfig[vpssIndex].layerHeight.set(layer.getOutHeight());

        // 拼缝处理
        result.vpssLayerConfig[vpssIndex].pieceEnable.set((short) (layer.getLeftCompensation() | layer.getRightCompensation()));

        // 优先级
        result.portConfig[0].layerPriority[vpssIndex].set(priority + globalLayerIndex);
        layerIndex++;
        globalLayerIndex++;
      }

      // 底图配置
      result.portConfig[screenIndex].backCaptionConfig.offsetX.set((int) screen.screenBounds.getMinX());
      result.portConfig[screenIndex].backCaptionConfig.offsetY.set((int) screen.screenBounds.getMinY());
      result.portConfig[screenIndex].backCaptionConfig.width.set((int) screen.screenBounds.getWidth());
      result.portConfig[screenIndex].backCaptionConfig.height.set((int) screen.screenBounds.getHeight());
    }

    // 空闲图层优先级
    int lastUpdatedPriorityIndex = 0;
    int currentPriority = 1;
    while (lastUpdatedPriorityIndex < Vp7Constants.MAX_PRIORITY_COUNT) {
      if (result.portConfig[0].layerPriority[lastUpdatedPriorityIndex].get() == 0) {
        result.portConfig[0].layerPriority[lastUpdatedPriorityIndex].set(currentPriority);
        currentPriority++;
      }
      lastUpdatedPriorityIndex++;
    }

    // 输出端口配置
    for (int i = 0; i < Vp7Constants.MAX_SCREEN_COUNT; i++) {
      // TODO 默认全部使能
      // 1表示使能，0表示不使能，开启强制使能下有效
      short enable = 1;
      // 0表示关闭强制使能，1表示开启强制使能，冗余模式下有图层时不强制使能
      int validScreenIndex = getValidScreenIndex(infType, i);
      short force = (short) (videoWall.getOsdData().isEnableRedundant()
          && Optional.ofNullable(layerCountPerScreen.get(validScreenIndex)).orElse(0) > 0 ? 0 : 1);
      result.portConfig[i].enable.set((short) (enable | (force << 1)));
    }

    // 输出分辨率配置
    try {
      Vp7ConfigData.Vp7OutputResData outputResData = videoWall.createVp7OutputData();
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      outputResData.write(baos);
      result.outputResData.read(new ByteArrayInputStream(baos.toByteArray()));
    } catch (IOException exception) {
      // Ignore
      exception.printStackTrace();
    }

    // 图层叠加配置
    result.overlapConfig.bgimgEnable.set(osd.isEnableBgImg() ? (short) 1 : 0);
    result.overlapConfig.bgimgAlpha.set((short) 0); // 默认背景图不透明
    result.overlapConfig.layerEnable.set((short) 1);
    result.overlapConfig.layerAlpha.set((short) 0); // 默认图层不透明
    result.overlapConfig.captionEnable.set(osd.isEnableBanner() ? (short) 1 : 0);
    result.overlapConfig.logoEnable.set((short) (osd.getOsdAlpha() > 0 ? 1 : 0));
    result.overlapConfig.logoAlpha.set((short) (255 - osd.getOsdAlpha()));
    result.overlapConfig.tpgEnable.set((short) (result.tpgConfig.mode.get() != 0 ? 1 : 0));
    result.overlapConfig.tpgAlpha.set((short) (255 - tpgConfig.color.get() >> 24));

    switch (infType) {
      case TYPE_SINGLE:
        result.overlapConfig.interfaceMode.set((short) 2);
        break;
      case TYPE_DOUBLE:
        result.overlapConfig.interfaceMode.set((short) 1);
        break;
      case TYPE_QUADRUPLE:
        result.overlapConfig.interfaceMode.set((short) 0);
        break;
      default:
        break;
    }

    result.overlapConfig.analogAudioSel.set((short) 0); // TODO 根据最后切换的信号？
    return result;
  }

  public static int getVp7Id(int screenRxId) {
    // Implementation here
    return screenRxId & 0xffff;
  }

  /**
   * 获取屏幕索引.
   */
  static int getScreenIndex(int screenRxId) {
    return screenRxId >> 16;
  }

  /**
   * 获取每个屏幕的最大图层数.
   */
  public static int getMaxLayerCountPerScreen(Vp7InfType infType) {
    int result = 0;
    switch (infType) {
      case TYPE_SINGLE:
        result = Vp7Constants.MAX_LAYER_COUNT_PER_SINGLE_SCREEN;
        break;
      case TYPE_DOUBLE:
        result = Vp7Constants.MAX_LAYER_COUNT_PER_DOUBLE_SCREEN;
        break;
      case TYPE_QUADRUPLE:
        result = Vp7Constants.MAX_LAYER_COUNT_PER_QUADRUPLE_SCREEN;
        break;
      default:
        break;
    }
    return result;
  }

  // 判断屏幕是否有效
  static boolean isScreenValid(Vp7InfType infType, int screenIndex) {
    if (screenIndex >= Vp7Constants.MAX_SCREEN_COUNT) {
      return false;
    }
    switch (infType) {
      case TYPE_SINGLE:
        return screenIndex == 0;
      case TYPE_DOUBLE:
        return screenIndex == 0 || screenIndex == 2;
      case TYPE_QUADRUPLE:
        return true;
      default:
        return false;
    }
  }

  // 获取有效的屏幕索引
  static int getValidScreenIndex(Vp7InfType infType, int screenIndex) {
    if (screenIndex >= Vp7Constants.MAX_SCREEN_COUNT) {
      return 0;
    }
    switch (infType) {
      case TYPE_DOUBLE:
        return screenIndex & 0x01;
      case TYPE_QUADRUPLE:
        return screenIndex;
      default:
        return 0;
    }
  }

  // 获取屏幕的起始VPSS索引
  static int getStartVpssIndexForScreen(Vp7InfType infType, int screenIndex) {
    if (!isScreenValid(infType, screenIndex)) {
      return -1;
    }
    int result = -1;
    switch (infType) {
      case TYPE_SINGLE:
        result = 0;
        break;
      case TYPE_DOUBLE:
        result = (screenIndex / 2) * Vp7Constants.MAX_LAYER_COUNT_PER_DOUBLE_SCREEN;
        break;
      case TYPE_QUADRUPLE:
        result = screenIndex * Vp7Constants.MAX_LAYER_COUNT_PER_QUADRUPLE_SCREEN;
        break;
      default:
        break;
    }
    return result;
  }

  static List<VpVideoDataWrapper> split4k30Videos(VpVideoDataWrapper video4k) {
    VpResolution resolution = video4k.getResolution();
    VpTxClipData txClipData = video4k.getTxClipData();

    // 分成左右两半
    final int leftInWidth = 1920;
    final int rightInWidth = resolution.width - leftInWidth;
    final int clippedLeftInWidth = Math.max(leftInWidth - txClipData.getLeft() - Math.max(0, txClipData.getRight() - rightInWidth), 0);

    // 4舍5入
    final int leftOutWidth = roundToEven(video4k.getWidth() * clippedLeftInWidth * 1.0 / video4k.getClippedResolutionWidth());
    final int rightOutWidth = video4k.getWidth() - leftOutWidth;

    VpSimpleVideoData leftVideo =
        new VpSimpleVideoData(video4k.getLeft(), video4k.getTop(), leftOutWidth, video4k.getHeight(), video4k.getAlpha(),
            video4k.getTxId());
    VpResolution leftResolution = new VpResolution(leftInWidth, resolution.height, VpResolution.Type.VIDEO_2K);

    VpTxClipData leftTxClipData =
        new VpTxClipData(txClipData.getLeft(), txClipData.getTop(), Math.max(0, txClipData.getRight() - rightInWidth),
            txClipData.getBottom());

    VpVideoDataWrapper leftVideoWrapper =
        new VpVideoDataWrapper(leftVideo, leftResolution, leftTxClipData, video4k.getIndex(), true);
    leftVideoWrapper.setPort(video4k.getPort());
    leftVideoWrapper.setChannel(video4k.getChannel());

    VpSimpleVideoData rightVideo =
        new VpSimpleVideoData(video4k.getLeft() + leftOutWidth, video4k.getTop(), rightOutWidth, video4k.getHeight(),
            video4k.getAlpha(), video4k.getTxId());
    VpResolution rightResolution = new VpResolution(rightInWidth, resolution.height, VpResolution.Type.VIDEO_2K);

    VpTxClipData rightTxClipData =
        new VpTxClipData(Math.max(0, txClipData.getLeft() - leftInWidth), txClipData.getTop(), txClipData.getRight(),
            txClipData.getBottom());

    VpVideoDataWrapper rightVideoWrapper =
        new VpVideoDataWrapper(rightVideo, rightResolution, rightTxClipData, video4k.getIndex(), true);
    rightVideoWrapper.setPort(video4k.getPort());
    if (video4k.getChannel() >= 0) {
      rightVideoWrapper.setChannel(video4k.getChannel() + 1); // 右半部分使用下一个通道
    } else {
      rightVideoWrapper.setChannel(-1);
    }

    List<VpVideoDataWrapper> result = new ArrayList<>();
    result.add(leftVideoWrapper);
    result.add(rightVideoWrapper);
    return result;
  }

  static Rectangle2D getIntersection(Rectangle2D first, Rectangle2D second) {
    if (!first.intersects(second)) {
      return null;
    }
    double minX = Math.max(first.getMinX(), second.getMinX());
    double minY = Math.max(first.getMinY(), second.getMinY());
    double maxX = Math.min(first.getMaxX(), second.getMaxX());
    double maxY = Math.min(first.getMaxY(), second.getMaxY());
    double width = maxX - minX;
    double height = maxY - minY;
    if (width == 0 || height == 0) {
      return null;
    }
    return new Rectangle2D(minX, minY, width, height);
  }

  /**
   * 获取连接信息.
   *
   * @param matrix VP矩阵
   * @param vpConsoleData Vpcon
   * @param currCfg 当前配置
   * @return 连接信息
   */
  public static List<SwitchMultiviewAllVideoChannel> getConnections(VpMatrix matrix, VpConsoleData vpConsoleData,
                                                                    Vp7WallCurrCfg currCfg) {
    List<SwitchMultiviewAllVideoChannel> channels = currCfg.getConnection(vpConsoleData.getId()).stream().map((Integer id) -> {
      ExtenderData extenderData = matrix.findExt(id);
      return extenderData == null ? null : extenderData.getCpuData();
    }).map((CpuData cpuData) -> {
      SwitchMultiviewAllVideoChannel channel = new SwitchMultiviewAllVideoChannel();
      channel.setCpuData(cpuData);
      channel.setSourceIndex(0);
      return channel;
    }).collect(Collectors.toList());
    return channels;
  }

  /**
   * 返回最接近的偶数.
   *
   * @param value 输入值
   * @return 最接近的偶数，输入值是整数，返回值为输入值减一
   */
  private static int roundToEven(double value) {
    int ceil = (int) Math.ceil(value);
    return ceil & ~1;
  }

  private static int roundToEven(int value) {
    return value & ~1;
  }

  private static int ceilToEven(int value) {
    return value % 2 == 0 ? value : value + 1;
  }

  private static int floorToEven(int value) {
    return value & ~1;
  }
}
