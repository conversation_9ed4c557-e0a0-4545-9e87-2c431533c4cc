package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarVideoWallFuncManager;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarIrregularCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbRxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.menu.MenuDelete;
import java.util.ArrayList;
import java.util.List;

/**
 * .
 */
public class MenuDelete4Caesar extends MenuDelete {
  private final CaesarDeviceController deviceController;
  private final CaesarVideoWallFuncManager videoWallFuncManager;

  /** Constructor. */
  public MenuDelete4Caesar(
      SystemEditControllable controllable,
      CaesarDeviceController deviceController,
      CaesarVideoWallFuncManager videoWallFuncManager) {
    super(controllable);
    this.deviceController = deviceController;
    this.videoWallFuncManager = videoWallFuncManager;
  }

  @Override
  protected void onAction() {
    for (VisualEditNode node : controllable.getSelectedNodes()) {
      if (node instanceof CaesarCrossScreenFunc) {
        CaesarCrossScreenFunc func = (CaesarCrossScreenFunc) node;
        deviceController.execute(
            () -> deviceController.deleteMultiScreenData(func.getDeviceData()));
      } else if (node instanceof CaesarVideoWallFunc) {
        int index = ((CaesarVideoWallFunc) node).getVideoWallIndex();
        deviceController.deleteVideoWall(index);
        // fix bug #1454. 删除视频墙时删除连接.
        List<VpConsoleData> vpConsoleDataList = new ArrayList<>();
        for (VisualEditNode child : node.getChildren()) {
          if (child instanceof VpGroup) {
            vpConsoleDataList.add(((VpGroup) child).getVpConsoleData());
          }
        }
        deviceController.resetVpCons(vpConsoleDataList);

        videoWallFuncManager.setVideoWallFunc(index, null);
      } else if (node instanceof CaesarUsbRxTerminal) {
        deviceController.execute(
            () ->
                deviceController.deleteUsbExtender(((CaesarUsbRxTerminal) node).getExtenderData()));
      } else if (node instanceof CaesarUsbTxTerminal) {
        deviceController.execute(
            () ->
                deviceController.deleteUsbExtender(((CaesarUsbTxTerminal) node).getExtenderData()));
      } else if (node instanceof CaesarIrregularCrossScreenFunc) {
        CaesarIrregularCrossScreenFunc func = (CaesarIrregularCrossScreenFunc) node;
        deviceController.execute(
            () -> deviceController.deleteMultiScreenData(func.getDeviceData()));
      } else if (node instanceof CpuGroup) {
        deviceController.execute(
            () -> deviceController.deleteTxGroup(((CpuGroup) node).getTxGroupData()));
      }
    }
    super.onAction();
  }
}
