package com.mc.tool.caesar.vpm.pages.extenderupdate.txgroup;

import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.TxGroupData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.lang.ref.WeakReference;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.SelectionMode;
import javafx.scene.input.MouseEvent;
import javafx.stage.Window;
import org.controlsfx.control.CheckListView;

/**
 * .
 */
public class InvalidTxGroupsManagerView extends UndecoratedDialog<ButtonType> {

  private final WeakReference<CaesarDeviceController> controllerRef;
  private final CheckListView<TxGroupData> checkListView = new CheckListView<>();
  private final ObservableList<TxGroupData> txGroups = FXCollections.observableArrayList();
  private boolean checkAll = false;

  /** Construct. */
  public InvalidTxGroupsManagerView(Window owner, CaesarDeviceController deviceController) {
    this.controllerRef = new WeakReference<>(deviceController);
    initOwner(owner);
    setTitle(CaesarI18nCommonResource.getString("txGroupsManager.title"));
    checkListView.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);
    checkListView.setItems(txGroups);
    getDialogPane().setContent(checkListView);
    updateData();
    getDialogPane().getButtonTypes().addAll(ButtonType.CLOSE, ButtonType.FINISH, ButtonType.APPLY);
    Node closeNode = getDialogPane().lookupButton(ButtonType.CLOSE);
    if (closeNode instanceof Button) {
      Button button = (Button) closeNode;
      button.addEventFilter(
          MouseEvent.MOUSE_PRESSED,
          event -> {
            destroy();
            event.consume();
          });
    }
    Node applyNode = getDialogPane().lookupButton(ButtonType.APPLY);
    if (applyNode instanceof Button) {
      Button button = (Button) applyNode;
      button.setText(CaesarI18nCommonResource.getString("offline_manager.delete_btn"));
      button.addEventFilter(
          MouseEvent.MOUSE_PRESSED,
          event -> {
            if (!checkListView.getCheckModel().getCheckedItems().isEmpty()) {
              deleteTxgroup();
              updateData();
            }
            event.consume();
          });
    }
    Node finishNode = getDialogPane().lookupButton(ButtonType.FINISH);
    if (finishNode instanceof Button) {
      Button button = (Button) finishNode;
      button.setText(CaesarI18nCommonResource.getString("txGroupsManager.selectAll"));
      button.addEventFilter(
          MouseEvent.MOUSE_PRESSED,
          event -> {
            if (checkAll) {
              checkListView.getCheckModel().clearChecks();
              checkAll = false;
            } else {
              checkListView.getCheckModel().checkAll();
              checkAll = true;
            }
            event.consume();
          });
    }
  }

  private void updateData() {
    CaesarDeviceController controller = controllerRef.get();
    if (controller != null) {
      Collection<TxGroupData> activeTxGroups =
          controller.getDataModel().getConfigDataManager().getActiveTxGroups();
      Collection<CpuData> allCpus = controller.getDataModel().getConfigDataManager().getCpus();

      List<CpuData> cpusWithGroup =
          allCpus.stream()
              .filter(CpuData::isOnline)
              .filter(item -> item.getTxGroupIndex() > 0)
              .collect(Collectors.toList());
      Set<Integer> collect =
          cpusWithGroup.stream()
              .map(CpuData::getTxGroupIndex)
              .map(item -> item - 1)
              .collect(Collectors.toSet());
      List<TxGroupData> txGroupDataList =
          activeTxGroups.stream()
              .filter(item -> !collect.contains(item.getOid()))
              .sorted()
              .collect(Collectors.toList());
      txGroups.setAll(txGroupDataList);
    }
  }

  private void deleteTxgroup() {
    CaesarDeviceController controller = controllerRef.get();
    if (controller != null) {
      controller.execute(
          () -> controller.deleteTxGroupsDirectly(checkListView.getCheckModel().getCheckedItems()));
    }
  }

  public void destroy() {
    txGroups.clear();
  }
}
