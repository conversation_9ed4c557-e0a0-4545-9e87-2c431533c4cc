package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.google.common.collect.Maps;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.ScenarioData;
import com.mc.tool.caesar.api.datamodel.TxGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.utils.DataChecker;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.crossscreen.datamodel.CaesarCrossScreenData.CrossScreenMode;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarDataUtility;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarVideoWallFuncManager;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.data.GridMatrixItem;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.event.VisualEditFuncBeginUpdateEvent;
import com.mc.tool.framework.event.VisualEditFuncEndUpdateEvent;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.EventBusProvider;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javafx.scene.control.AlertEx.AlertExType;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
@SuppressFBWarnings("BC_UNCONFIRMED_CAST_OF_RETURN_VALUE")
public class CaesarData2VisualDataModel {
  private WeakReference<CaesarDeviceController> controllerRef;
  private WeakReference<VisualEditModel> modelRef;

  @Getter
  private CaesarVideoWallFuncManager videoWallFuncManager = new CaesarVideoWallFuncManager();

  public CaesarData2VisualDataModel() {
  }

  public void setDeviceController(CaesarDeviceController controller) {
    controllerRef = new WeakReference<>(controller);
  }

  public void setModel(VisualEditModel model) {
    modelRef = new WeakReference<>(model);
  }

  public CaesarDeviceController getDeviceController() {
    return controllerRef.get();
  }

  protected VisualEditModel getModel() {
    return modelRef.get();
  }

  /**
   * 加载所有，一般是测试使用.
   */
  public void loadAll() {
    Collection<MatrixDefinitionData> datas =
        Utilities.getActiveMatrices(getDeviceController().getDataModel());
    for (MatrixDefinitionData data : datas) {
      CaesarMatrix matrix = new CaesarMatrix();
      matrix.init();
      matrix.setName(data.getDevice());
      matrix.setMatrixData(data);
      getModel().addItem(matrix);
      loadData(matrix, false);
    }
  }

  /**
   * 加载设备数据.
   *
   * @param matrix         矩阵
   * @param onlyConnection 只加载连接信息.
   */
  public void loadData(CaesarMatrix matrix, boolean onlyConnection) {
    List<VisualEditTerminal> collect =
        matrix.getAllTerminalChild().stream()
            .filter(
                item -> {
                  if (item instanceof CaesarTerminalBase) {
                    return ((CaesarTerminalBase) item).getId() == 0;
                  }
                  return false;
                })
            .collect(Collectors.toList());
    for (VisualEditTerminal terminal : collect) {
      matrix.removeTerminal(terminal);
    }
    if (getDeviceController().getDataModel().isOnlyConfig()) {
      loadExtenders(matrix);
    } else {
      loadPorts(matrix);
    }

    if (!onlyConnection) {
      loadVpcons(matrix);
      loadMultiScreens(matrix);
      loadVideoWalls(matrix);
      if (getDeviceController()
          .getDataModel()
          .getConfigMetaData()
          .getUtilVersion()
          .hasTxGroupData()) {
        loadTxGroup(matrix);
      }
    }
  }

  /**
   * 加载Tx分组.
   */
  public void loadTxGroup(CaesarMatrix matrix) {
    Collection<TxGroupData> activeTxGroups =
        getDeviceController().getDataModel().getConfigDataManager().getActiveTxGroups();
    List<Integer> activeTxGroupsOid =
        activeTxGroups.stream().map(AbstractData::getOid).collect(Collectors.toList());
    Map<Integer, CpuGroup> existGroupMap = Maps.newHashMap();
    // 删除无效tx分组
    for (CpuGroup group : matrix.getAllCpuGroup()) {
      if (group.getTxGroupData() == null
          || !activeTxGroupsOid.contains(group.getTxGroupData().getOid())) {
        getModel().deleteGroup(group);
        continue;
      }
      List<CpuData> cpuDatas =
          CaesarDataUtility.getCpuDataUsedByCpuGroup(
              getDeviceController().getDataModel().getConfigDataManager(), group.getTxGroupData());
      Set<Integer> realCpuDataIdSet =
          cpuDatas.stream().map(CpuData::getId).collect(Collectors.toSet());
      Set<Integer> cpuDataIdSet = new HashSet<>();
      for (VisualEditNode child : group.getChildren()) {
        if (child instanceof CaesarCpuTerminal) {
          cpuDataIdSet.add(((CaesarCpuTerminal) child).getCpuData().getId());
        }
      }
      if (!realCpuDataIdSet.equals(cpuDataIdSet)) {
        getModel().deleteGroup(group);
        continue;
      }
      existGroupMap.put(group.getTxGroupData().getOid(), group);
    }
    // 创建当前没有的tx分组
    for (TxGroupData activeTxGroup : activeTxGroups) {
      if (existGroupMap.containsKey(activeTxGroup.getOid())) {
        existGroupMap.get(activeTxGroup.getOid()).setName(activeTxGroup.getName());
        continue;
      }
      List<CpuData> cpuDatas =
          CaesarDataUtility.getCpuDataUsedByCpuGroup(
              getDeviceController().getDataModel().getConfigDataManager(), activeTxGroup);
      if (cpuDatas.isEmpty()) {
        getDeviceController().execute(() -> getDeviceController().deleteTxGroup(activeTxGroup));
      } else {
        VisualEditNode[] visualEditNodes =
            cpuDatas.stream()
                .map(matrix::findTerminal)
                .filter(Objects::nonNull)
                .toArray(VisualEditNode[]::new);
        if (visualEditNodes.length > 0) {
          CpuGroup cpuGroup =
              getModel().addGroup(activeTxGroup.getName(), CpuGroup.class, visualEditNodes);
          if (cpuGroup != null) {
            cpuGroup.setTxGroupData(activeTxGroup);
          }
        }
      }
    }
  }

  /**
   * 加载矩阵间的拓扑关系.
   *
   * @return 拓扑关系.
   */
  public Collection<GridMatrixItem> loadMatrixTopological(List<CaesarMatrix> matrices) {
    List<GridMatrixItem> items = new ArrayList<>();
    CaesarDeviceController controller = controllerRef.get();
    if (controller == null) {
      return items;
    }
    int size = matrices.size();
    for (int i = 0; i < size; i++) {
      GridMatrixItem item = new GridMatrixItem();
      item.getNameProperty().bind(matrices.get(i).nameProperty());
      item.setPosition(i);
      items.add(item);
    }
    // 查找矩阵间的连接关系
    for (PortData portData : controller.getDataModel().getConfigDataManager().getAvailablePorts()) {
      if (!portData.isStatusMatrix()) {
        continue;
      }
      int port1 = portData.getOid() + 1;
      int port2 = portData.getType();

      int port1MatrixIndex = -1;
      int port2MatrixIndex = -1;
      for (int i = 0; i < size; i++) {
        MatrixDefinitionData matrixDefinitionData = matrices.get(i).getMatrixData();
        if (port1 >= matrixDefinitionData.getFirstPort()
            && port1 <= matrixDefinitionData.getLastPort()) {
          port1MatrixIndex = i;
        }
        if (port2 >= matrixDefinitionData.getFirstPort()
            && port2 <= matrixDefinitionData.getLastPort()) {
          port2MatrixIndex = i;
        }
      }

      if (port1MatrixIndex >= 0 && port2MatrixIndex >= 0 && port1MatrixIndex != port2MatrixIndex) {
        GridMatrixItem item1 = items.get(port1MatrixIndex);
        GridMatrixItem item2 = items.get(port2MatrixIndex);
        item1.addRelatedMatrix(item2, port1, port2);
        item2.addRelatedMatrix(item1, port2, port1);
      }
    }

    return items;
  }

  /**
   * 加载主机大屏数据到CaesarVideoWallFunc.
   *
   * @param model               逻辑数据
   * @param dataModel           主机数据
   * @param videoWallIndex      大屏索引
   * @param caesarVideoWallFunc 逻辑大屏
   * @param matrix              逻辑主机
   */
  public static void loadVideoWall4Index(
      VisualEditModel model, CaesarSwitchDataModel dataModel, int videoWallIndex,
      CaesarVideoWallFunc caesarVideoWallFunc, CaesarMatrix matrix) {
    VideoWallGroupData.VideoWallData videoWallData =
        dataModel.getVpDataModel().getVideoWallData(videoWallIndex);
    CaesarDataUtility.vpVideoWallData2CaesarVideoWallData(
        model,
        videoWallData,
        (CaesarVideoWallData) caesarVideoWallFunc.getVideoWallObject(),
        dataModel.getConfigDataManager(),
        matrix);

    // 复制名称
    if (!videoWallData.getName().isEmpty()) {
      caesarVideoWallFunc.setName(videoWallData.getName());
    } else {
      videoWallData.setName(caesarVideoWallFunc.getName());
    }
    ScenarioData[] scenarioDatas =
        dataModel.getVpDataModel().getScenarioDatas();
    // 计算当前视频墙预案索引的范围
    int scenarioBegin =
        videoWallIndex * (CaesarConstants.SCENARIO_SIZE / VideoWallGroupData.GROUP_COUNT);
    int scenarioEnd =
        scenarioBegin + CaesarConstants.SCENARIO_SIZE / VideoWallGroupData.GROUP_COUNT;
    // 复制预案
    Set<VideoWallObject> retainScenarios = new HashSet<>();
    for (ScenarioData scenarioData : scenarioDatas) {
      if (scenarioData.getIndex() < scenarioBegin || scenarioData.getIndex() >= scenarioEnd) {
        continue;
      }
      CaesarVideoWallData caesarScenario = null;
      for (VideoWallObject object : caesarVideoWallFunc.getScenarios()) {
        CaesarVideoWallData temp = (CaesarVideoWallData) object;
        if (temp.getId() == scenarioData.getIndex()) {
          caesarScenario = temp;
          break;
        }
      }
      // 如果未找到预案，创建一个
      if (caesarScenario == null) {
        caesarScenario = new CaesarVideoWallData();
        caesarScenario.init();
        caesarVideoWallFunc.addScenario(caesarScenario);
      }

      CaesarDataUtility.vpVideoWallData2CaesarVideoWallData(
          model,
          scenarioData.getVideoWallData(),
          caesarScenario,
          dataModel.getConfigDataManager(),
          matrix);
      caesarScenario.getName().set(scenarioData.getConfigName());
      caesarScenario.setId(scenarioData.getIndex());
      retainScenarios.add(caesarScenario);
    }
    Set<VideoWallObject> removeScenarios = new HashSet<>(caesarVideoWallFunc.getScenarios());
    removeScenarios.removeAll(retainScenarios);
    for (VideoWallObject object : removeScenarios) {
      caesarVideoWallFunc.removeScenario(object);
    }
  }

  protected void loadVideoWalls(CaesarMatrix matrix) {
    Set<Integer> existVideoWallIndex = new HashSet<>();
    List<CaesarVideoWallFunc> removeVideoWalls = new ArrayList<>();
    Set<VpConsoleData> usedVpcons = new HashSet<>();
    // 复制已有的视频墙的数据
    for (VisualEditFunc func : matrix.getAllFunctions()) {
      if (func instanceof CaesarVideoWallFunc) {
        CaesarVideoWallFunc videoWallFunc = (CaesarVideoWallFunc) func;
        int videoWallIndex = videoWallFunc.getVideoWallIndex();
        if (videoWallIndex < 0 || videoWallIndex >= VideoWallGroupData.GROUP_COUNT) {
          removeVideoWalls.add(videoWallFunc);
          continue;
        }
        if (existVideoWallIndex.contains(videoWallIndex)) {
          removeVideoWalls.add(videoWallFunc);
          continue;
        }
        // 如果视频墙数据无效，删除
        VideoWallGroupData.VideoWallData vpVideoWallData =
            getDeviceController().getDataModel().getVpDataModel().getVideoWallData(videoWallIndex);
        if (!vpVideoWallData.isDataValid()) {
          removeVideoWalls.add(videoWallFunc);
          continue;
        }
        // 检查children是否同步
        Collection<VpGroup> remoteUsedVpGroups =
            CaesarDataUtility.getVpGroupUsedByVpVideoWallData(
                getModel(),
                getDeviceController().getDataModel().getConfigDataManager(),
                matrix,
                vpVideoWallData);
        Set<VpGroup> localVpGroupSet = new HashSet<>();
        for (VisualEditNode node : videoWallFunc.getChildren()) {
          if (node instanceof VpGroup) {
            localVpGroupSet.add((VpGroup) node);
          }
        }
        Set<VpGroup> remoteUsedVpGroupSet = new HashSet<>(remoteUsedVpGroups);
        if (!localVpGroupSet.equals(remoteUsedVpGroupSet)) {
          for (VpGroup vpGroup : localVpGroupSet) {
            getModel().moveToBase(vpGroup, matrix);
          }
          for (VpGroup vpGroup : remoteUsedVpGroups) {
            getModel().moveToBase(vpGroup, videoWallFunc);
          }
        }
        //
        existVideoWallIndex.add(videoWallIndex);
        EventBusProvider.getEventBus().post(new VisualEditFuncBeginUpdateEvent(videoWallFunc));
        try {
          loadVideoWall4Index(getModel(), getDeviceController().getDataModel(), videoWallIndex,
              videoWallFunc,
              matrix);
          CaesarVideoWallData videoWallData =
              (CaesarVideoWallData) videoWallFunc.getVideoWallObject();
          usedVpcons.addAll(CaesarDataUtility.getUsedVpconFromVideoWall(videoWallData));
        } finally {
          EventBusProvider.getEventBus().post(new VisualEditFuncEndUpdateEvent(videoWallFunc));
        }
      }
    }
    for (CaesarVideoWallFunc func : removeVideoWalls) {
      getModel().deleteGroup(func);
    }

    // 创建当前没有的视频墙
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      if (existVideoWallIndex.contains(i)) {
        continue;
      }

      VideoWallGroupData.VideoWallData videoWallData =
          getDeviceController().getDataModel().getVpDataModel().getVideoWallData(i);
      if (!videoWallData.isDataValid()) {
        continue;
      }

      Collection<VpGroup> vpGroups =
          CaesarDataUtility.getVpGroupUsedByVpVideoWallData(
              getModel(),
              getDeviceController().getDataModel().getConfigDataManager(),
              matrix,
              videoWallData);
      if (vpGroups.isEmpty()) {
        continue;
      }
      for (VpGroup vpGroup : vpGroups) {
        if (usedVpcons.contains(vpGroup.getVpConsoleData())) {
          log.error(
              "Video wall {} uses a vpcon({}) used by others.",
              i,
              vpGroup.getVpConsoleData().getId());
          continue;
        }
        usedVpcons.add(vpGroup.getVpConsoleData());
        getModel().moveTo(vpGroup, matrix);
      }
      CaesarVideoWallFunc videoWallGroup =
          getModel()
              .addGroup(
                  "VideoWall" + (i + 1),
                  CaesarVideoWallFunc.class,
                  vpGroups.toArray(new VpGroup[0]));
      if (videoWallGroup != null) {
        CaesarVideoWallFunc caesarVideoWallFunc = videoWallGroup;
        caesarVideoWallFunc.setVideoWallIndex(i);
        loadVideoWall4Index(getModel(), getDeviceController().getDataModel(), i,
            caesarVideoWallFunc, matrix);
      }
    }
    initVideoWallFuncs();
  }

  protected void loadMultiScreens(CaesarMatrix matrix) {
    Set<MultiScreenData> findedMultiScreen = new HashSet<>();
    Set<VisualEditFunc> deletedFunc = new HashSet<>();
    //
    for (VisualEditFunc func : matrix.getAllFunctions()) {
      if (func instanceof CaesarCrossScreenFunc) {
        CaesarCrossScreenFunc crossScreenFunc = (CaesarCrossScreenFunc) func;
        MultiScreenData data =
            getDeviceController()
                .getDataModel()
                .getConfigDataManager()
                .getMultiScreenData(crossScreenFunc.getMultiScreenOid());
        if (data == null || !data.isStatusActive() || data.isIrregular()) {
          deletedFunc.add(func);
        } else {
          EventBusProvider.getEventBus().post(new VisualEditFuncBeginUpdateEvent(crossScreenFunc));
          try {
            // 检查children同步
            Collection<VisualEditTerminal> remoteTerminals =
                CaesarDataUtility.getMultiscreenUsedTerminals(
                    getDeviceController().getDataModel().getConfigDataManager(), matrix, data);
            Set<VisualEditTerminal> remoteTerminalSet = new HashSet<>(remoteTerminals);
            Set<VisualEditTerminal> localTerminalSet =
                new HashSet<>(crossScreenFunc.getAllTerminalChild());
            if (!localTerminalSet.equals(remoteTerminalSet)) {
              for (VisualEditTerminal terminal : localTerminalSet) {
                getModel().moveToBase(terminal, matrix);
              }
              for (VisualEditTerminal terminal : remoteTerminals) {
                getModel().moveToBase(terminal, crossScreenFunc);
              }
            }
            //
            crossScreenFunc.setDeviceData(data);
            crossScreenFunc.reload(matrix, getDeviceController());
            findedMultiScreen.add(data);
          } finally {
            EventBusProvider.getEventBus().post(new VisualEditFuncEndUpdateEvent(crossScreenFunc));
          }
        }
      } else if (func instanceof CaesarIrregularCrossScreenFunc) {
        CaesarIrregularCrossScreenFunc crossScreenFunc = (CaesarIrregularCrossScreenFunc) func;
        MultiScreenData data =
            getDeviceController()
                .getDataModel()
                .getConfigDataManager()
                .getMultiScreenData(crossScreenFunc.getMultiScreenOid());
        if (data == null || !data.isStatusActive() || !data.isIrregular()) {
          deletedFunc.add(func);
        } else {
          EventBusProvider.getEventBus().post(new VisualEditFuncBeginUpdateEvent(crossScreenFunc));
          try {
            // 检查children同步
            Collection<VisualEditTerminal> remoteTerminals =
                CaesarDataUtility.getMultiscreenUsedTerminals(
                    getDeviceController().getDataModel().getConfigDataManager(), matrix, data);
            Set<VisualEditTerminal> remoteTerminalSet = new HashSet<>(remoteTerminals);
            Set<VisualEditTerminal> localTerminalSet =
                new HashSet<>(crossScreenFunc.getAllTerminalChild());
            if (!localTerminalSet.equals(remoteTerminalSet)) {
              for (VisualEditTerminal terminal : localTerminalSet) {
                getModel().moveToBase(terminal, matrix);
              }
              for (VisualEditTerminal terminal : remoteTerminals) {
                getModel().moveToBase(terminal, crossScreenFunc);
              }
            }
            //
            crossScreenFunc.setDeviceData(data);
            crossScreenFunc.reload(matrix, getDeviceController());
            findedMultiScreen.add(data);
          } finally {
            EventBusProvider.getEventBus().post(new VisualEditFuncEndUpdateEvent(crossScreenFunc));
          }
        }
      }
    }
    // 删除设备不存在的跨屏
    for (VisualEditFunc func : deletedFunc) {
      getModel().deleteGroup(func);
    }
    // 创建设备有而当前没有的跨屏
    for (MultiScreenData data :
        getDeviceController().getDataModel().getConfigDataManager().getActiveMultiScreen()) {
      if (findedMultiScreen.contains(data)) {
        continue;
      }

      Collection<VisualEditTerminal> nodes =
          CaesarDataUtility.getMultiscreenUsedTerminals(
              getDeviceController().getDataModel().getConfigDataManager(), matrix, data);
      for (VisualEditTerminal terminal : nodes) {
        getModel().moveTo(terminal, matrix);
      }
      if (data.getType() == CrossScreenMode.AUTO.ordinal()
          || data.getType() == CrossScreenMode.MANUAL.ordinal()) {
        CaesarCrossScreenFunc func =
            getModel()
                .addGroup(
                    data.getName(),
                    CaesarCrossScreenFunc.class,
                    nodes.toArray(new VisualEditNode[0]));
        if (func != null) {
          func.setDeviceData(data);
          func.reload(matrix, getDeviceController());
        }
      } else if (data.getType() == CrossScreenMode.IRREGULAR.ordinal()) {
        CaesarIrregularCrossScreenFunc func =
            getModel()
                .addGroup(
                    data.getName(),
                    CaesarIrregularCrossScreenFunc.class,
                    nodes.toArray(new VisualEditNode[0]));
        if (func != null) {
          func.setDeviceData(data);
          func.reload(matrix, getDeviceController());
        }
      }
    }
  }

  protected void loadVpcons(CaesarMatrix matrix) {
    // 添加vpcon

    Set<VpGroup> toRemoveGroups = new HashSet<>(matrix.getAllVpGroup());
    for (VpConsoleData vpConsoleData :
        this.getDeviceController().getDataModel().getConfigDataManager().getActiveVpconsolses()) {
      VpGroup vpGroup = matrix.findVpGroup(vpConsoleData);
      boolean needToAdd = false;
      if (vpGroup == null) {
        vpGroup = new VpGroup(vpConsoleData);
        vpGroup.init();
        needToAdd = true;
      } else {
        vpGroup.setVpConsoleData(vpConsoleData);
      }
      toRemoveGroups.remove(vpGroup);
      Set<VisualEditNode> toRemoveChildren = new HashSet<>(vpGroup.getChildren());
      for (ConsoleData consoleData : vpConsoleData.getInPortList()) {
        if (consoleData == null) {
          continue;
        }
        CaesarTerminalBase terminal = matrix.findTerminal(consoleData);
        if (terminal == null) {
          continue;
        }
        toRemoveChildren.remove(terminal);
        if (!vpGroup.hasChild(terminal)) {
          getModel().moveTo(terminal, vpGroup);
        }
      }
      for (VisualEditNode node : toRemoveChildren) {
        getModel().moveTo(node, matrix);
      }

      if (needToAdd) {
        matrix.addChildren(vpGroup);
      }
    }

    // 删除无用的vpgroup，避免主机出错后无法恢复的情况
    for (VpGroup item : toRemoveGroups) {
      getModel().deleteGroup(item);
    }
  }

  protected void loadConsoles(CaesarMatrix matrix) {
    List<VisualEditTerminal> oldRxs = new ArrayList<>(matrix.getAllRxChildTerminal());
    List<VisualEditTerminal> newRxs = new ArrayList<>();
    for (ConsoleData consoleData :
        this.getDeviceController().getDataModel().getConfigDataManager().getActiveConsoles()) {
      ExtenderData extenderData = consoleData.getExtenderData(0);
      CaesarTerminalBase terminal = matrix.findTerminal(consoleData);
      if (terminal == null) {
        terminal = new CaesarConTerminal(consoleData);
        terminal.init();
        int matrixPort = extenderData == null ? 0 : extenderData.getPort();
        int terminalPort = CaesarTerminalBase.CAESAR_NORMAL_PORT;
        if (matrixPort != 0) {
          matrix.insertTerminal(terminal, matrixPort, terminalPort);
        }
        matrixPort = extenderData == null ? 0 : extenderData.getRdPort();
        terminalPort = CaesarTerminalBase.CAESAR_RD_PORT;
        if (matrixPort != 0) {
          matrix.insertTerminal(terminal, matrixPort, terminalPort);
        }
      } else {
        ((CaesarConTerminal) terminal).setConsoleData(consoleData);
        matrix.updateTerminalPort(
            terminal, extenderData.getPort(), CaesarTerminalBase.CAESAR_NORMAL_PORT);
        matrix.updateTerminalPort(
            terminal, extenderData.getRdPort(), CaesarTerminalBase.CAESAR_RD_PORT);
      }
      newRxs.add(terminal);
    }

    oldRxs.removeAll(newRxs);
    for (VisualEditTerminal terminal : oldRxs) {
      if (terminal instanceof CaesarTerminalBase) {
        ((CaesarTerminalBase) terminal).reset();
      }
    }
  }

  protected CaesarTerminalBase createTerminal(ExtenderData extenderData) {
    if (extenderData == null) {
      return null;
    }
    if (extenderData.isCpuType()) {
      if (extenderData.isUsbCpuType()) {
        return new CaesarUsbTxTerminal(extenderData);
      } else {
        if (extenderData.getCpuData() == null) {
          return null;
        } else {
          return new CaesarCpuTerminal(extenderData.getCpuData());
        }
      }
    } else if (extenderData.isConType()) {
      if (extenderData.isUsbConType()) {
        return new CaesarUsbRxTerminal(extenderData);
      } else {
        if (extenderData.getConsoleData() == null) {
          return null;
        } else {
          return new CaesarConTerminal(extenderData.getConsoleData());
        }
      }
    } else {
      return null;
    }
  }

  protected void updateTerminal(CaesarTerminalBase terminal, ExtenderData extenderData) {
    if (terminal instanceof CaesarCpuTerminal) {
      ((CaesarCpuTerminal) terminal).setCpuData(extenderData.getCpuData());
    } else if (terminal instanceof CaesarConTerminal) {
      ((CaesarConTerminal) terminal).setConsoleData(extenderData.getConsoleData());
    } else if (terminal instanceof CaesarUsbTxTerminal) {
      ((CaesarUsbTxTerminal) terminal).setExtenderData(extenderData);
    } else if (terminal instanceof CaesarUsbRxTerminal) {
      ((CaesarUsbRxTerminal) terminal).setExtenderData(extenderData);
    }
  }

  protected void loadExtenders(CaesarMatrix matrix) {
    List<VisualEditTerminal> oldTerminals = new ArrayList<>(matrix.getAllTerminalChild());
    List<VisualEditTerminal> newTerminals = new ArrayList<>();
    for (ExtenderData extenderData :
        getDeviceController().getDataModel().getConfigDataManager().getActiveExtenders()) {
      CaesarTerminalBase terminal = loadExtender(matrix, extenderData);
      if (terminal == null) {
        continue;
      }
      newTerminals.add(terminal);
    }
    oldTerminals.removeAll(newTerminals);
    for (VisualEditTerminal terminal : oldTerminals) {
      if (terminal instanceof CaesarTerminalBase) {
        ((CaesarTerminalBase) terminal).reset();
      }
    }
  }

  private CaesarTerminalBase loadExtender(CaesarMatrix matrix, ExtenderData extenderData) {
    // 检查外设是否在当前矩阵
    if (extenderData == null) {
      return null;
    }
    MatrixDefinitionData matrixData = matrix.getMatrixData();
    if (matrixData != null
        && !Utilities.isExtenderInMatrix(
        getDeviceController().getDataModel(), matrixData, extenderData)) {
      return null;
    }
    CaesarTerminalBase terminal = matrix.findTerminal(extenderData);
    if (terminal == null) {
      CaesarTerminalBase temp = createTerminal(extenderData);
      if (temp == null) {
        log.error("Fail to create terminal for extender : " + extenderData.getId());
        return null;
      }
      terminal = temp;
      terminal.init();
      int matrixPort = extenderData.getPort();
      int terminalPort = CaesarTerminalBase.CAESAR_NORMAL_PORT;
      if (matrixPort != 0) {
        matrix.insertTerminal(terminal, matrixPort, terminalPort);
      }
      matrixPort = extenderData.getRdPort();
      terminalPort = CaesarTerminalBase.CAESAR_RD_PORT;
      if (matrixPort != 0) {
        matrix.insertTerminal(terminal, matrixPort, terminalPort);
      }
    } else {
      updateTerminal(terminal, extenderData);
      matrix.updateTerminalPort(
          terminal, extenderData.getPort(), CaesarTerminalBase.CAESAR_NORMAL_PORT);
      matrix.updateTerminalPort(
          terminal, extenderData.getRdPort(), CaesarTerminalBase.CAESAR_RD_PORT);
    }
    return terminal;
  }

  /**
   * 检查配置数据中的错误.
   */
  public void checkErrors() {
    List<PortData> errorPortList = new ArrayList<>();
    List<ExtenderData> errorExtenderList = new ArrayList<>();
    List<CpuData> errorCpuList = new ArrayList<>();
    List<ConsoleData> errorConsoleList = new ArrayList<>();
    List<MultiScreenData> errorMultiscreenList = new ArrayList<>();
    DataChecker.checkErrors(
        getDeviceController().getDataModel(),
        errorPortList,
        errorExtenderList,
        errorCpuList,
        errorConsoleList,
        true);
    DataChecker.checkMultiscreen(getDeviceController().getDataModel(), errorMultiscreenList, true);

    int maxLengthSize = 20;
    StringBuilder builder = new StringBuilder();
    if (!errorPortList.isEmpty()) {
      builder.append("Following ports' data is error : ");
      int lastLength = 0;
      for (int i = 0; i < errorPortList.size(); i++) {
        builder.append(errorPortList.get(i).getOid() + 1);
        if (i != errorPortList.size() - 1) {
          builder.append(", ");
        }
        if (builder.length() - lastLength > maxLengthSize) {
          builder.append("\n\t");
          lastLength = builder.length();
        }
      }
      builder.append("\n");
    }
    if (!errorExtenderList.isEmpty()) {
      builder.append("Following EXTs' data is error : ");
      int lastLength = 0;
      for (int i = 0; i < errorExtenderList.size(); i++) {
        builder.append(errorExtenderList.get(i).getName());
        if (i != errorExtenderList.size() - 1) {
          builder.append(", ");
        }
        if (builder.length() - lastLength > maxLengthSize) {
          builder.append("\n\t");
          lastLength = builder.length();
        }
      }
      builder.append("\n");
    }
    if (!errorCpuList.isEmpty()) {
      builder.append("Following TXs' data is error : ");
      int lastLength = 0;
      for (int i = 0; i < errorCpuList.size(); i++) {
        builder.append(errorCpuList.get(i).getName());
        if (i != errorCpuList.size() - 1) {
          builder.append(", ");
        }
        if (builder.length() - lastLength > maxLengthSize) {
          builder.append("\n\t");
          lastLength = builder.length();
        }
      }
      builder.append("\n");
    }

    if (!errorConsoleList.isEmpty()) {
      builder.append("Following RXs' data is error : ");
      int lastLength = 0;
      for (int i = 0; i < errorConsoleList.size(); i++) {
        builder.append(errorConsoleList.get(i).getName());
        if (i != errorConsoleList.size() - 1) {
          builder.append(", ");
        }
        if (builder.length() - lastLength > maxLengthSize) {
          builder.append("\n\t");
          lastLength = builder.length();
        }
      }
      builder.append("\n");
    }
    if (builder.length() > 0) {
      builder.append("Please try to refresh to resolve this problem.");
      PlatformUtility.runInFxThreadLater(
          () ->
              ViewUtility.showAlert(
                  null, CaesarI18nCommonResource.getString("check.try"), AlertExType.ERROR));
    }
  }

  protected void loadPorts(CaesarMatrix matrix) {
    MatrixDefinitionData data = matrix.getMatrixData();
    if (data == null) {
      return;
    }

    List<VisualEditTerminal> oldTerminals = new ArrayList<>(matrix.getAllTerminalChild());
    List<VisualEditTerminal> newTerminals = new ArrayList<>();

    for (PortData portData : getDeviceController().getDataModel().getConfigData().getPortDatas()) {
      // 只处理在线设备或者usb设备.
      if (!portData.isStatusAvailable()
          && (portData.getExtenderData() == null || !portData.getExtenderData().isUsbType())) {
        continue;
      }
      int port = portData.getOid() + 1;
      if (port < data.getFirstPort() || port > data.getLastPort()) {
        continue;
      }

      if (portData.isStatusMatrix()) {
        CaesarGridLineTerminal gridLineTerminal = loadPort(matrix, portData, port);
        newTerminals.add(gridLineTerminal);
      } else if (!DataChecker.checkPort(portData, false)) {
        continue;
      } else {
        CaesarTerminalBase terminal = loadExtender(matrix, portData.getExtenderData());
        if (terminal != null) {
          newTerminals.add(terminal);
        }
      }
    }

    oldTerminals.removeAll(newTerminals);

    for (VisualEditTerminal terminal : oldTerminals) {
      if (terminal instanceof CaesarTerminalBase) {
        ((CaesarTerminalBase) terminal).reset();
      } else {
        matrix.removeTerminal(terminal);
      }
    }
  }

  private CaesarGridLineTerminal loadPort(CaesarMatrix matrix, PortData portData, int port) {
    CaesarGridLineTerminal gridLineTerminal = matrix.findGridLine(port);
    if (gridLineTerminal == null) {
      gridLineTerminal = new CaesarGridLineTerminal();
      gridLineTerminal.setName(CaesarI18nCommonResource.getString("systemedit.gridline"));
      gridLineTerminal.setPortIndex(port);
      gridLineTerminal.setPortData(portData);
      matrix.insertTerminal(gridLineTerminal, port, 1);
    } else {
      gridLineTerminal.setPortData(portData);
      matrix.updateTerminalPort(gridLineTerminal, port, 1);
    }
    return gridLineTerminal;
  }

  protected void loadCpus(CaesarMatrix matrix) {
    List<VisualEditTerminal> oldTxs = new ArrayList<>(matrix.getAllTxChildTerminal());
    List<VisualEditTerminal> newTxs = new ArrayList<>();
    for (CpuData cpuData :
        this.getDeviceController().getDataModel().getConfigDataManager().getActiveCpus()) {
      ExtenderData extenderData = cpuData.getExtenderData(0);
      CaesarTerminalBase terminal = matrix.findTerminal(extenderData);
      if (terminal == null) {
        terminal = new CaesarCpuTerminal(cpuData);
        terminal.init();
        int matrixPort = extenderData == null ? 0 : extenderData.getPort();
        int terminalPort = CaesarTerminalBase.CAESAR_NORMAL_PORT;
        if (matrixPort != 0) {
          matrix.insertTerminal(terminal, matrixPort, terminalPort);
        }
        matrixPort = extenderData == null ? 0 : extenderData.getRdPort();
        terminalPort = CaesarTerminalBase.CAESAR_RD_PORT;
        if (matrixPort != 0) {
          matrix.insertTerminal(terminal, matrixPort, terminalPort);
        }
      } else {
        ((CaesarCpuTerminal) terminal).setCpuData(cpuData);
        matrix.updateTerminalPort(
            terminal, extenderData.getPort(), CaesarTerminalBase.CAESAR_NORMAL_PORT);
        matrix.updateTerminalPort(
            terminal, extenderData.getRdPort(), CaesarTerminalBase.CAESAR_RD_PORT);
      }
      newTxs.add(terminal);
    }
    oldTxs.removeAll(newTxs);
    for (VisualEditTerminal terminal : oldTxs) {
      if (terminal instanceof CaesarTerminalBase) {
        ((CaesarTerminalBase) terminal).reset();
      }
    }
  }

  /**
   * 查找所有的视频墙func并保存到videoWallFuncs.
   */
  private void initVideoWallFuncs() {
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      videoWallFuncManager.setVideoWallFunc(i, null);
    }
    for (VisualEditFunc func : getModel().getAllOnlineFuncs()) {
      if (func instanceof CaesarVideoWallFunc) {
        CaesarVideoWallFunc caesarVideoWallFunc = (CaesarVideoWallFunc) func;
        int videoWallIndex = caesarVideoWallFunc.getVideoWallIndex();
        if (videoWallIndex >= 0 && videoWallIndex < VideoWallGroupData.GROUP_COUNT) {
          if (videoWallFuncManager.getVideoWallFunc(videoWallIndex) != null) {
            log.warn("Dulplicate video wall index : {} !", videoWallIndex);
          }
          videoWallFuncManager.setVideoWallFunc(videoWallIndex, caesarVideoWallFunc);
        }
      }
    }
  }
}
