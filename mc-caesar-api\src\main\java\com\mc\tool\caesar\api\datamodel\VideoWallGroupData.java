package com.mc.tool.caesar.api.datamodel;

import com.google.common.primitives.Ints;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.vp.ColorUtility;
import com.mc.tool.caesar.api.datamodel.vp.LayoutInfoGetter;
import com.mc.tool.caesar.api.datamodel.vp.LayoutInfoSetter;
import com.mc.tool.caesar.api.datamodel.vp.LayoutSize;
import com.mc.tool.caesar.api.datamodel.vp.OsdInfoGetter;
import com.mc.tool.caesar.api.datamodel.vp.OsdInfoSetter;
import com.mc.tool.caesar.api.datamodel.vp.OutputInfoGetter;
import com.mc.tool.caesar.api.datamodel.vp.OutputInfoSetter;
import com.mc.tool.caesar.api.datamodel.vp.PreviewConfigData;
import com.mc.tool.caesar.api.datamodel.vp.TestFrameData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.io.NormalStruct;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.logging.Logger;
import javafx.scene.paint.Color;

/**
 * .
 */
@SuppressFBWarnings("URF_UNREAD_FIELD")
public class VideoWallGroupData extends NormalStruct {

  private static final Logger LOG = Logger.getLogger(VideoWallGroupData.class.getName());
  public static final int GROUP_COUNT = 16; //8
  public static final int VIDEOWALL_SCENARIO_COUNT = CaesarConstants.SCENARIO_SIZE / GROUP_COUNT;
  public static final int VIDEOWALL_SCENARIO_RESERVED_DATA_COUNT = 126451;
  public static final int MAX_VPCON_COUNT = 200;
  public static final String CURRENT_VERSION = "2.0";
  public static final int VIDEOWALL_DATA_SIZE = 0xf000;

  private VideoWallGroupData.VideoWallData[] videoWalls =
      array(new VideoWallGroupData.VideoWallData[GROUP_COUNT]);
  private GlobalData globalData = inner(new GlobalData());

  @Override
  public void write(OutputStream out) throws IOException {
    for (int i = 0; i < GROUP_COUNT; i++) {
      videoWalls[i].write(out);
      out.write(new byte[VIDEOWALL_DATA_SIZE - videoWalls[i].size()]);
    }
    getGlobalData().write(out);
  }

  @Override
  public int read(InputStream in) throws IOException {
    int size = 0;
    for (int i = 0; i < GROUP_COUNT; i++) {
      videoWalls[i].read(in);
      int leftSize = VIDEOWALL_DATA_SIZE - videoWalls[i].size();
      while (leftSize > 0) {
        int read = in.read(new byte[leftSize]);
        if (read <= 0) {
          throw new IOException();
        }
        leftSize -= read;
      }
      size += VIDEOWALL_DATA_SIZE;
    }
    getGlobalData().read(in);
    size += getGlobalData().size();
    return size;
  }

  /**
   * .
   */
  public VideoWallData getData(int index) {
    if (index < 0 || index >= GROUP_COUNT) {
      return null;
    }
    return videoWalls[index];
  }

  /**
   * 获取有效视频墙数据个数.
   */
  public int getValidDataCount() {
    return (int) Arrays.stream(videoWalls).filter(VideoWallData::isDataValid).count();
  }

  /**
   * .
   */
  public void setData(int index, VideoWallData videoWall) {
    if (index < 0 || index >= GROUP_COUNT) {
      return;
    }
    videoWalls[index] = videoWall;
  }

  public GlobalData getGlobalData() {
    return globalData;
  }

  /**
   * .
   */
  public static class GlobalData extends NormalStruct {

    /**
     * 版本信息，格式为MEDIACOMM_[version].
     */
    private Utf8String version = new Utf8String(32);
    private PreviewData singlePreview = inner(new PreviewData());
    private PreviewData thumbnailPreview = inner(new PreviewData());

    public GlobalData() {
      setVersion(CURRENT_VERSION);
    }

    public String getVersion() {
      return version.toString();
    }

    public void setVersion(String version) {
      this.version.set(version);
    }

    public PreviewData getSinglePreview() {
      return singlePreview;
    }

    public PreviewData getThumbnailPreview() {
      return thumbnailPreview;
    }
  }

  /**
   * .
   */
  public static class PreviewData extends NormalStruct {

    public static final int INVALID_CONSOLE_ID = 0xFEFEFEFE;
    private Signed32 previewConId = new Signed32();
    private Utf8String previewAddr = new Utf8String(64);

    /**
     * .
     */
    public void getPreviewConfig(PreviewConfigData configData) {
      try {
        configData.setPreviewRtspUrl(new URI(previewAddr.get()));
      } catch (Exception ex) {
        ex.printStackTrace();
      }
    }

    public int getPreviewConId() {
      return previewConId.get();
    }

    public void setPreviewConId(int previewConId) {
      this.previewConId.set(previewConId);
    }

    public String getPreviewAddr() {
      return previewAddr.get();
    }

    public void setPreviewAddr(String previewAddr) {
      this.previewAddr.set(previewAddr);
    }

  }

  /**
   * .
   */
  public static class CompleteVideoData extends NormalStruct {

    public static final int INVALID_CPU_ID = 0xFEFEFEFE;
    private Utf8String name = new Utf8String(16);
    private Signed32 cpuId = new Signed32();
    private Signed32 left = new Signed32();
    private Signed32 top = new Signed32();
    private Unsigned16 width = new Unsigned16();
    private Unsigned16 height = new Unsigned16();
    private Unsigned16 alpha = new Unsigned16();
    private Unsigned16 videoId = new Unsigned16();
    private Unsigned8 sourceIndex = new Unsigned8();

    private Unsigned16 clipIndex = new Unsigned16(); // SourceClipData索引+1
    private Unsigned16 audioRx = new Unsigned16(); // 用于输出音频的RX ID,0表示无输出
    @SuppressWarnings("unused")
    private Unsigned8[] reserved = array(new Unsigned8[7]);

    public CompleteVideoData() {

    }

    public String getName() {
      return name.get();
    }

    public void setName(String name) {
      this.name.set(name);
    }

    public int getLeft() {
      return left.get();
    }

    public void setLeft(int left) {
      this.left.set(left);
    }

    public int getTop() {
      return top.get();
    }

    public void setTop(int top) {
      this.top.set(top);
    }

    public int getWidth() {
      return width.get();
    }

    public void setWidth(int width) {
      this.width.set(width);
    }

    public int getHeight() {
      return height.get();
    }

    public void setHeight(int height) {
      this.height.set(height);
    }

    public int getCpuId() {
      return cpuId.get();
    }

    public void setCpuId(int id) {
      cpuId.set(id);
    }

    public int getVideoId() {
      return videoId.get();
    }

    public void setVideoId(int videoId) {
      this.videoId.set(videoId);
    }

    public double getAlpha() {
      return alpha.get() / 255.0;
    }

    public void setAlpha(double value) {
      alpha.set((int) (value * 255));
    }

    public int getSourceIndex() {
      return sourceIndex.get();
    }

    public void setSourceIndex(int index) {
      sourceIndex.set((short) index);
    }

    public int getClipIndex() {
      return clipIndex.get();
    }

    public void setClipIndex(int index) {
      clipIndex.set((short) index);
    }

    public int getAudioRx() {
      return audioRx.get();
    }

    public void setAudioRx(int audioRx) {
      this.audioRx.set((short) audioRx);
    }
  }

  /**
   * .
   */
  public static class CompleteScreenData extends NormalStruct {

    public static final int INVALID_CON_ID = 0xFEFEFEFE;
    private Signed16 oid = new Signed16();

    private Signed32 conId = new Signed32();
    @SuppressWarnings("unused")
    private Unsigned8[] reserved = array(new Unsigned8[10]);

    /**
     * .
     */
    public void set(ScreenData screenData) {
      oid.set((short) screenData.getOid());

      VpConsoleData vpConsoleData = screenData.getConsole();
      if (vpConsoleData != null) {
        conId.set(vpConsoleData.getId());
      } else {
        resetId();
      }
    }

    public void setOid(short oid) {
      this.oid.set(oid);
    }

    /**
     * .
     */
    public void resetId() {
      conId.set(INVALID_CON_ID);
    }

    /**
     * 获取数据.
     *
     * @param screenData 用于接收数据
     */
    public void get(ScreenData screenData) {
      screenData.setOid(oid.get());
    }

    public long getConId() {
      return conId.get();
    }
  }

  /**
   * .
   */
  public static class VideoWallData extends NormalStruct
      implements OsdInfoGetter, LayoutInfoGetter {

    public static final int MAX_CON_COUNT = 200;
    public static final int MAX_VIDEO_COUNT = 200;
    public static final int MAX_WIDTH_COUNT = 50;
    public static final int MAX_HEIGHT_COUNT = 20;
    public static final int MAX_VPCON_COUNT = 50;
    /**
     * 版本信息，格式为MEDIACOMM_[version].
     */
    private Utf8String name = new Utf8String(32);
    private Unsigned16 columns = new Unsigned16();
    private Unsigned16 rows = new Unsigned16();
    private Unsigned16 videoCnt = new Unsigned16();
    private Unsigned16 resolutionWidth = new Unsigned16();
    private Unsigned16 resolutionHeight = new Unsigned16();
    private Unsigned16 osdLeft = new Unsigned16();
    private Unsigned16 osdTop = new Unsigned16();
    private Unsigned16 osdWidth = new Unsigned16();
    private Unsigned16 osdHeight = new Unsigned16();
    private Unsigned16 osdAlpha = new Unsigned16();
    private Unsigned32 osdColor = new Unsigned32();
    private Unsigned32 bgColor = new Unsigned32();
    private Unsigned8 type = new Unsigned8();
    private BitField enableBanner = new BitField(1);
    private BitField enableBannerBg = new BitField(1);
    @SuppressWarnings("unused")
    private BitField reservedFlags = new BitField(6);
    private Vp6OutputData vpOutputData = inner(new Vp6OutputData());
    private Unsigned8 enableMultiResolution = new Unsigned8();
    private Unsigned8 outputCount = new Unsigned8();
    private Unsigned8 showLogo = new Unsigned8();
    private Unsigned8 vpgroupCount = new Unsigned8();
    private Unsigned8 enableBgImg = new Unsigned8();
    private Unsigned16 bgImgWidth = new Unsigned16();
    private Unsigned16 bgImgHeight = new Unsigned16();
    private BitField disableSyncData = new BitField(1);
    private BitField enableRedundant = new BitField(1);
    @SuppressWarnings("unused")
    private BitField reservedBits = new BitField(6);
    private Unsigned8 compensationScaleThreshold = new Unsigned8();
    private BitField leftCompensation = new BitField(4);
    private BitField topCompensation = new BitField(4);
    private BitField rightCompensation = new BitField(4);
    private BitField bottomCompensation = new BitField(4);
    @SuppressWarnings("unused")
    private Unsigned8[] compensationReserved = array(new Unsigned8[5]);
    private TestFrameData testFrameData = inner(new TestFrameData());
    private Unsigned16 audioGroupIndex = new Unsigned16();
    @SuppressWarnings("unused")
    private Unsigned8[] reserved = array(new Unsigned8[66]);
    private CompleteScreenData[] vpCons = array(new CompleteScreenData[MAX_CON_COUNT]);
    private CompleteVideoData[] vpCpus = array(new CompleteVideoData[MAX_VIDEO_COUNT]);
    private Unsigned16[] widths = array(new Unsigned16[MAX_WIDTH_COUNT]);
    private Unsigned16[] heights = array(new Unsigned16[MAX_HEIGHT_COUNT]);
    private Signed16[] horzMargins = array(new Signed16[MAX_WIDTH_COUNT]);
    private Signed16[] vertMargins = array(new Signed16[MAX_HEIGHT_COUNT]);
    private Unsigned8[] reserved1 = array(new Unsigned8[1]);

    public VideoWallData() {

    }

    /**
     * 打印基础信息.
     */
    public void printBaseInfo() {

      String msg = String.format(
          "name(%s), col(%d), row(%d), vc(%d), rw(%d), rh(%d), "
              + "ol(%d), ot(%d), ow(%d), oh(%d), oa(%d), oc(#%x), "
              + "bc(#%x), mulr(%d), oc(%d), sl(%d)", name.get(), columns.get(), rows.get(),
          videoCnt.get(), resolutionWidth.get(), resolutionHeight.get(), osdLeft.get(),
          osdTop.get(), osdWidth.get(), osdHeight.get(), osdAlpha.get(), (int) osdColor.get(),
          (int) bgColor.get(), (int) enableMultiResolution.get(), (int) outputCount.get(),
          (int) showLogo.get());
      LOG.info(msg);
      vpOutputData.print();
    }

    /**
     * .
     */
    public boolean isDataValid() {
      int col = getColumns();
      int row = getRows();
      int wid = resolutionWidth.get();
      int height = resolutionHeight.get();
      boolean multiResEnable = isMultipleResolutionEnable();
      if (multiResEnable) {
        return isValueValid(col) && isValueValid(row) && (col * row) <= MAX_CON_COUNT
            && videoCnt.get() <= MAX_VIDEO_COUNT;
      } else {
        return isValueValid(col) && isValueValid(row) && (col * row) <= MAX_CON_COUNT
            && videoCnt.get() <= MAX_VIDEO_COUNT && isValueValid(wid) && isValueValid(height);
      }
    }

    protected boolean isValueValid(int value) {
      return value != 0 && value != 0xffff;
    }

    public String getName() {
      return name.toString();
    }

    public void setName(String name) {
      this.name.set(name);
    }

    public int getColumns() {
      return columns.get();
    }

    public void setColumns(int columns) {
      this.columns.set(columns);
    }

    public int getRows() {
      return rows.get();
    }

    public void setRows(int rows) {
      this.rows.set(rows);
    }

    public int getVideoCnt() {
      return videoCnt.get();
    }

    public void setVideoCnt(int videoCnt) {
      this.videoCnt.set(videoCnt);
    }

    /**
     * 获取分辨率的宽度.
     */
    @Override
    public int getResolutionWidth() {
      if (vpOutputData.enable.get() == 1) {
        return vpOutputData.horzOutput.activeVideoTime.get();
      } else {
        return resolutionWidth.get();
      }
    }

    public void setResolutionWidth(int resolutionWidth) {
      this.resolutionWidth.set(resolutionWidth);
    }

    /**
     * 获取分辨率的高度.
     */
    @Override
    public int getResolutionHeight() {
      if (vpOutputData.enable.get() == 1) {
        return vpOutputData.vertOutput.activeVideoTime.get();
      } else {
        return resolutionHeight.get();
      }
    }

    public void setResolutionHeight(int resolutionHeight) {
      this.resolutionHeight.set(resolutionHeight);
    }

    /**
     * 向视频墙设置输出数据.
     *
     * @param outputData 输出数据
     */
    public void setOutput(OutputInfoGetter outputData, int width, int height) {
      vpOutputData.enable.set((short) (outputData.isEnable() ? 1 : 0));
      vpOutputData.clock.set(outputData.getClock());
      vpOutputData.vertOutput.activeVideoTime.set(height);
      vpOutputData.vertOutput.backPorch.set(outputData.getVertBackPorch());
      vpOutputData.vertOutput.frontPorch.set(outputData.getVertFrontPorch());
      vpOutputData.vertOutput.polarity.set((short) (outputData.isVertPolarity() ? 1 : 0));
      vpOutputData.vertOutput.sync.set(outputData.getVertSync());
      vpOutputData.horzOutput.activeVideoTime.set(width);
      vpOutputData.horzOutput.backPorch.set(outputData.getHorzBackPorch());
      vpOutputData.horzOutput.frontPorch.set(outputData.getHorzFrontPorch());
      vpOutputData.horzOutput.polarity.set((short) (outputData.isHorzPolarity() ? 1 : 0));
      vpOutputData.horzOutput.sync.set(outputData.getHorzSync());
    }

    /**
     * 向视频墙获取输出数据.
     *
     * @param outputData 输出数据
     */
    public void getOutput(OutputInfoSetter outputData) {
      outputData.setEnable(vpOutputData.enable.get() == 1);
      outputData.setClock(vpOutputData.clock.get());
      outputData.setVertBackPorch(vpOutputData.vertOutput.backPorch.get());
      outputData.setVertFrontPorch(vpOutputData.vertOutput.frontPorch.get());
      outputData.setVertPolarity(vpOutputData.vertOutput.polarity.get() == 1);
      outputData.setVertSync(vpOutputData.vertOutput.sync.get());

      outputData.setHorzBackPorch(vpOutputData.horzOutput.backPorch.get());
      outputData.setHorzFrontPorch(vpOutputData.horzOutput.frontPorch.get());
      outputData.setHorzPolarity(vpOutputData.horzOutput.polarity.get() == 1);
      outputData.setHorzSync(vpOutputData.horzOutput.sync.get());
    }

    /**
     * 获取输出数据.
     *
     * @return 输出数据.
     */
    public Vp6OutputData getOutput() {
      try {
        return (Vp6OutputData) vpOutputData.clone();
      } catch (CloneNotSupportedException exception) {
        return null;
      }
    }

    /**
     * .
     */
    public void setLayout(LayoutInfoGetter layoutData) {
      rows.set(layoutData.getVertCount());
      columns.set(layoutData.getHorzCount());
      resolutionWidth.set(layoutData.getResolutionWidth());
      resolutionHeight.set(layoutData.getResolutionHeight());
      enableMultiResolution.set((short) (layoutData.isMultipleResolutionEnable() ? 1 : 0));
      if (layoutData.isMultipleResolutionEnable()) {
        int index = 0;
        for (int value : layoutData.getResolutionWidths()) {
          widths[index].set(value);
          index++;
        }
        index = 0;
        for (int value : layoutData.getResolutionHeights()) {
          heights[index].set(value);
          index++;
        }
        index = 0;
        for (int value : layoutData.getHorzMargins()) {
          horzMargins[index].set((short) value);
          index++;
        }
        index = 0;
        for (int value : layoutData.getVertMargins()) {
          vertMargins[index].set((short) value);
          index++;
        }
      }
      compensationScaleThreshold.set((short) layoutData.getCompensationScaleThreshold());
      leftCompensation.set(layoutData.getLeftCompensation());
      rightCompensation.set(layoutData.getRightCompensation());
      topCompensation.set(layoutData.getTopCompensation());
      bottomCompensation.set(layoutData.getBottomCompensation());
    }

    /**
     * 设置osd数据.
     *
     * @param layoutData osd数据
     */
    public void setOsd(OsdInfoGetter layoutData) {
      osdLeft.set(layoutData.getOsdLeft());
      osdTop.set(layoutData.getOsdTop());
      osdWidth.set(layoutData.getOsdWidth());
      osdHeight.set(layoutData.getOsdHeight());
      osdAlpha.set((int) (layoutData.getOsdAlpha() * 255));
      osdColor.set(ColorUtility.toRgb(layoutData.getOsdColor()));
      bgColor.set(ColorUtility.toRgb(layoutData.getBgColor()));
      showLogo.set((short) (layoutData.isShowLogo() ? 0 : 1));
      enableBgImg.set((short) (layoutData.isEnableBgImg() ? 1 : 0));
      bgImgWidth.set(layoutData.getBgImgWidth());
      bgImgHeight.set(layoutData.getBgImgHeight());
      disableSyncData.set((short) (layoutData.isDisableSyncData() ? 1 : 0));
      enableRedundant.set((short) (layoutData.isEnableRedundant() ? 1 : 0));
      enableBanner.set((short) (layoutData.isEnableBanner() ? 1 : 0));
      enableBannerBg.set((short) (layoutData.isEnableBannerBg() ? 1 : 0));
    }

    /**
     * .
     */
    public void getLayout(LayoutInfoSetter layoutData) {
      layoutData.setVertCount(getVertCount());
      layoutData.setHorzCount(getHorzCount());
      layoutData.setResolution(new LayoutSize(resolutionWidth.get(), resolutionHeight.get()));
      layoutData.setEnableMultipleResolution(isMultipleResolutionEnable());
      if (isMultipleResolutionEnable()) {
        List<Integer> widthList = new ArrayList<>();
        for (int i = 0; i < getHorzCount(); i++) {
          widthList.add(widths[i].get());
        }
        layoutData.setResolutionWidths(widthList);

        List<Integer> heightList = new ArrayList<>();
        for (int i = 0; i < getVertCount(); i++) {
          heightList.add(heights[i].get());
        }
        layoutData.setResolutionHeights(heightList);

        List<Integer> horzMarginList = new ArrayList<>();
        for (int i = 0; i < getHorzCount() - 1; i++) {
          horzMarginList.add((int) horzMargins[i].get());
        }
        layoutData.setHorzMargins(horzMarginList);

        List<Integer> vertMarginList = new ArrayList<>();
        for (int i = 0; i < getVertCount() - 1; i++) {
          vertMarginList.add((int) vertMargins[i].get());
        }
        layoutData.setVertMargins(vertMarginList);
      }

      layoutData.setCompensationScaleThreshold(getCompensationScaleThreshold());
      layoutData.setLeftCompensation(getLeftCompensation());
      layoutData.setRightCompensation(getRightCompensation());
      layoutData.setTopCompensation(getTopCompensation());
      layoutData.setBottomCompensation(getBottomCompensation());
    }

    /**
     * 获取osd数据.
     *
     * @param layoutData osd数据.
     */
    public void getOsd(OsdInfoSetter layoutData) {
      layoutData.setOsdLeft(getOsdLeft());
      layoutData.setOsdTop(getOsdTop());
      layoutData.setOsdWidth(getOsdWidth());
      layoutData.setOsdHeight(getOsdHeight());
      layoutData.setOsdAlpha(getOsdAlpha());
      layoutData.setOsdColor(getOsdColor());
      layoutData.setBgColor(getBgColor());
      layoutData.setShowLogo(isShowLogo());
      layoutData.setEnableBgImg(isEnableBgImg());
      layoutData.setBgImgWidth(getBgImgWidth());
      layoutData.setBgImgHeight(getBgImgHeight());
      layoutData.setDisableSyncData(isDisableSyncData());
      layoutData.setEnableRedundant(isEnableRedundant());
      layoutData.setEnableBanner(isEnableBanner());
      layoutData.setEnableBannerBg(isEnableBannerBg());
    }

    /**
     * .
     */
    public CompleteScreenData getCompleteScreenData(int index) {
      if (index < 0 || index >= vpCons.length) {
        return null;
      }
      return vpCons[index];
    }

    /**
     * .
     */
    public CompleteVideoData getCompleteVideoData(int index) {
      if (index < 0 || index >= vpCpus.length) {
        return null;
      }
      return vpCpus[index];
    }

    public int getVpgroupCount() {
      return vpgroupCount.get();
    }

    /**
     * 获取vpgroup.
     *
     * @param index index
     * @return id
     */
    public int getVpgroups(int index) {
      return 0;
    }

    /**
     * 添加vpgroup.
     *
     * @param vpgroup vpgroupid
     */
    public void addVpgroups(int vpgroup) {
      if (getVpgroupCount() < MAX_VPCON_COUNT - 1) {
        vpgroupCount.set((short) (getVpgroupCount() + 1));
      }
    }

    /**
     * 获取有效数据的区间.
     *
     * @return 区间数组，元素个数是偶数个， 排列顺序为：range1_start, range1_length, range2_start, range2_length
     */
    public int[] getValidDataRange() {
      List<Integer> rangeList = new ArrayList<>();
      // vpcon数据
      int count = rows.get() * columns.get();
      if (count > MAX_CON_COUNT) {
        LOG.warning("Error vpcon count ! Count = " + count);
        return new int[0];
      }

      if (count > 0) {
        int start = vpCons[0].offset();
        rangeList.add(start);
        if (count == MAX_CON_COUNT) {
          rangeList.add(vpCpus[0].offset() - start);
        } else {
          rangeList.add(vpCons[count].offset() - start);
        }
      }
      // video数据
      count = videoCnt.get();
      if (count > MAX_VIDEO_COUNT) {
        LOG.warning("Error video count ! Count = " + count);
        return new int[0];
      }
      if (count > 0) {
        int start = vpCpus[0].offset();
        rangeList.add(start);
        if (count == MAX_VIDEO_COUNT) {
          rangeList.add(widths[0].offset() - start);
        } else {
          rangeList.add(vpCpus[count].offset() - start);
        }
      }

      if (enableMultiResolution.get() == 1) {
        //widths
        count = columns.get();
        if (count > MAX_WIDTH_COUNT) {
          LOG.warning("Error width count ! Count = " + count);
        } else if (count > 0) {
          int start = widths[0].offset();
          rangeList.add(start);
          if (count == MAX_WIDTH_COUNT) {
            rangeList.add(heights[0].offset() - start);
          } else {
            rangeList.add(widths[count].offset() - start);
          }
        }

        //height
        count = rows.get();
        if (count > MAX_HEIGHT_COUNT) {
          LOG.warning("Error height count ! Count = " + count);
        } else if (count > 0) {
          int start = heights[0].offset();
          rangeList.add(start);
          if (count == MAX_HEIGHT_COUNT) {
            rangeList.add(horzMargins[0].offset() - start);
          } else {
            rangeList.add(heights[count].offset() - start);
          }
        }
        // horzMargins
        count = columns.get() - 1;
        if (count >= MAX_WIDTH_COUNT) {
          LOG.warning("Error width count ! Count = " + count);
        } else if (count > 0) {
          int start = horzMargins[0].offset();
          rangeList.add(start);
          if (count == MAX_WIDTH_COUNT - 1) {
            rangeList.add(vertMargins[0].offset() - start);
          } else {
            rangeList.add(horzMargins[count].offset() - start);
          }
        }
        // vertMargins
        count = rows.get() - 1;
        if (count >= MAX_HEIGHT_COUNT) {
          LOG.warning("Error height count ! Count = " + count);
        } else {
          int start = vertMargins[0].offset();
          rangeList.add(start);
          if (count == MAX_HEIGHT_COUNT - 1) {
            rangeList.add(reserved1[0].offset() - start);
          } else {
            rangeList.add(vertMargins[count].offset() - start);
          }
        }
      }

      //vpgroups
      return Ints.toArray(rangeList);

    }

    /**
     * .
     */
    public int[] getBaseDataRange() {
      int[] range = new int[2];
      range[0] = 0;
      range[1] = vpCons[0].offset();
      return range;
    }

    public short getType() {
      return type.get();
    }

    public void setType(short type) {
      this.type.set(type);
    }

    public boolean getEnableBanner() {
      return enableBanner.byteValue() == 1;
    }

    public void setEnableBanner(boolean enableBanner) {
      this.enableBanner.set((short) (enableBanner ? 1 : 0));
    }

    public boolean getEnableBannerBg() {
      return enableBannerBg.byteValue() == 1;
    }

    public void setEnableBannerBg(boolean enableBannerBg) {
      this.enableBannerBg.set((short) (enableBannerBg ? 1 : 0));
    }

    @Override
    public int getHorzCount() {
      return columns.get();
    }

    @Override
    public int getVertCount() {
      return rows.get();
    }

    @Override
    public int getOsdLeft() {
      return osdLeft.get();
    }

    @Override
    public int getOsdTop() {
      return osdTop.get();
    }

    @Override
    public int getOsdWidth() {
      return osdWidth.get();
    }

    @Override
    public int getOsdHeight() {
      return osdHeight.get();
    }

    @Override
    public double getOsdAlpha() {
      return osdAlpha.get() / 255.0;
    }

    @Override
    public Color getOsdColor() {
      return ColorUtility.fromeRgb((int) osdColor.get());
    }

    @Override
    public Color getBgColor() {
      return ColorUtility.fromeRgb((int) bgColor.get());
    }

    public boolean isVp6() {
      return type.get() == 0;
    }

    public boolean isVp7() {
      return type.get() == 1;
    }

    @Override
    public Collection<Integer> getResolutionWidths() {
      throw new RuntimeException("Not accessable!");
    }

    @Override
    public Collection<Integer> getResolutionHeights() {
      throw new RuntimeException("Not accessable!");
    }

    @Override
    public boolean isMultipleResolutionEnable() {
      return enableMultiResolution.get() == 1;
    }

    @Override
    public Collection<Integer> getHorzMargins() {
      throw new RuntimeException("Not accessable!");
    }

    @Override
    public Collection<Integer> getVertMargins() {
      throw new RuntimeException("Not accessable!");
    }

    @Override
    public boolean isShowLogo() {
      //为了适配旧数据，0表示显示，1表示不显示
      return showLogo.get() != 1;
    }

    @Override
    public boolean isEnableBgImg() {
      return enableBgImg.get() == 1;
    }

    @Override
    public int getBgImgWidth() {
      return bgImgWidth.get();
    }

    @Override
    public int getBgImgHeight() {
      return bgImgHeight.get();
    }

    @Override
    public boolean isDisableSyncData() {
      return disableSyncData.intValue() != 0;
    }

    @Override
    public boolean isEnableRedundant() {
      return enableRedundant.intValue() != 0;
    }

    @Override
    public boolean isEnableBanner() {
      return enableBanner.byteValue() == 1;
    }

    @Override
    public boolean isEnableBannerBg() {
      return enableBannerBg.byteValue() == 1;
    }

    @Override
    public int getCompensationScaleThreshold() {
      return compensationScaleThreshold.get();
    }

    @Override
    public int getLeftCompensation() {
      return leftCompensation.intValue();
    }

    @Override
    public int getRightCompensation() {
      return rightCompensation.intValue();
    }

    @Override
    public int getTopCompensation() {
      return topCompensation.intValue();
    }

    @Override
    public int getBottomCompensation() {
      return bottomCompensation.intValue();
    }

    public int getTestFrameSpeed() {
      return testFrameData.getSpeed();
    }

    public long getTestFrameColor() {
      return testFrameData.getColor();
    }

    public int getTestFrameMode() {
      return testFrameData.getMode();
    }

    public int getTestFrameAlpha() {
      return testFrameData.getAlpha();
    }

    public void setTestFrameSpeed(int speed) {
      testFrameData.setSpeed((short) speed);
    }

    public void setTestFrameColor(long color) {
      testFrameData.setColor(color);
    }

    public void setAlpha(int alpha) {
      testFrameData.setAlpha((short) alpha);
    }

    public void setTestFrameMode(int mode) {
      testFrameData.setMode((short) mode);
    }

    public int getAudioGroupIndex() {
      return audioGroupIndex.get();
    }

    public void setAudioGroupIndex(int audioGroupIndex) {
      this.audioGroupIndex.set(audioGroupIndex);
    }

  }
}
