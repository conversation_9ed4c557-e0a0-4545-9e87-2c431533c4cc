package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCrossScreenFunc;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.menu.MenuGroup;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.menu.predicate.TypePredicate;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedDialog;
import com.sun.javafx.scene.control.skin.resources.ControlResources;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.function.UnaryOperator;
import javafx.beans.binding.Bindings;
import javafx.geometry.Pos;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.ButtonType;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextFormatter.Change;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.stage.Modality;
import javafx.stage.Window;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class MenuCrossScreen4Caesar extends MenuItem {
  protected final SystemEditControllable controllable;
  protected final CaesarDeviceController deviceController;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuCrossScreen4Caesar(
      SystemEditControllable controllable, CaesarDeviceController deviceController) {
    this.controllable = controllable;
    this.deviceController = deviceController;
    this.setText(I18nUtility.getI18nBundle("systemedit").getString("menu.create_cross_screen"));
    this.setOnAction(event -> onAction());

    this.disableProperty().bind(getMenuDisableBinding(controllable));
  }

  /**
   * 获取菜单是否应该禁掉的binding.
   *
   * @param controllable controllable
   * @return binding
   */
  public static MenuPredicateBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = MenuGroup.getMenuDisableBinding(controllable);
    Predicate<VisualEditNode> notTypePredicate =
        new TypePredicate(VisualEditTerminal.class).negate();
    binding.addSingleSelectionPredicate(notTypePredicate);
    binding.addSingleSelectionPredicate(VisualEditNode::isTx);
    binding.addSingleSelectionPredicate(node -> !(node instanceof CaesarConTerminal));
    binding.addSingleSelectionPredicate(
        node -> node instanceof CaesarConTerminal && ((CaesarConTerminal) node).isVp());
    binding.addAllSelectionPredicate(nodes -> nodes.size() > CaesarConstants.MultiScreen.MAX_CON);
    binding.addSingleSelectionPredicate(
        node -> node instanceof CaesarConTerminal && ((CaesarConTerminal) node).isVp());
    binding.addSingleSelectionPredicate(node -> node instanceof CaesarConTerminal
        && ((CaesarConTerminal) node).getExtenderData().getExtenderStatusInfo().getSpecialExtType()
        == CaesarConstants.Extender.SpecialExtenderType.HW_PREVIEW);
    return binding;
  }

  protected void onAction() {
    Collection<CellSkin> skins = controllable.getGraph().getSelectionModel().getSelectedCellSkin();
    List<VisualEditNode> nodeList = new ArrayList<>();
    for (CellSkin skin : skins) {
      if (skin.getCell().getBindedObject() instanceof VisualEditNode) {
        nodeList.add((VisualEditNode) skin.getCell().getBindedObject());
      } else {
        log.warn(
            "Cell's bindedobject is not VisualEditNode but {}", skin.getCell().getBindedObject());
      }
    }
    MultiScreenData screenData =
        deviceController.getDataModel().getConfigDataManager().getFreeMultiScreenData();
    if (screenData == null) {
      ViewUtility.showAlert(
          getParentPopup().getOwnerWindow(),
          CaesarI18nCommonResource.getString("cross_screen.fail_to_create"),
          AlertExType.WARNING);
      return;
    }
    Optional<CrossScreenInfo> result =
        getCrossScreenInfoFromDialog(getParentPopup().getOwnerWindow(), "", nodeList.size());
    if (result.isPresent()) {
      CaesarCrossScreenFunc func =
          controllable.addGroup(
              result.get().getName(),
              CaesarCrossScreenFunc.class,
              nodeList.toArray(new VisualEditNode[0]));
      if (func != null) {
        func.setLayout(result.get().getRow(), result.get().getColumn());
        deviceController.execute(
            () -> {
              screenData.setStatusActive(true);
              screenData.setStatusNew(true);
            });
        func.setDeviceData(screenData);
        // 监听名称修改
        func.addNameListener(deviceController);

        // 提示切换到配置页面
        Optional<ButtonType> askResult =
            ViewUtility.getConfirmResultFromDialog(
                getParentPopup().getOwnerWindow(),
                CaesarI18nCommonResource.getString("page.switch"),
                CaesarI18nCommonResource.getString("page.switch_to_operation"));
        if (askResult.isPresent() && askResult.get() == ButtonType.YES) {
          ApplicationBase applicationBase =
              InjectorProvider.getInjector().getInstance(ApplicationBase.class);
          applicationBase
              .getViewManager()
              .switchToPage(controllable.getEntity(), CaesarOperationPageNew.NAME, func);
        }
      }
    }
  }

  /** 配置跨屏. */
  public static Optional<CrossScreenInfo> getCrossScreenInfoFromDialog(
      Window owner, String initText, int minCount) {
    NameDialog dialog = new NameDialog(minCount, initText);
    dialog.initOwner(owner);
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.setTitle(
        I18nUtility.getI18nBundle("systemedit").getString("cell.wizard.nameconfig_title"));
    dialog.setHeaderText(null);
    dialog.setContentText(
        I18nUtility.getI18nBundle("systemedit").getString("cell.wizard.nameconfig_content"));
    dialog.setGraphic(null);
    dialog.getDialogPane().setMinWidth(300);

    return dialog.showAndWait();
  }

  static class CrossScreenInfo {
    @Getter @Setter private String name;
    @Getter @Setter private int row;
    @Getter @Setter private int column;
  }

  static class NameDialog extends UndecoratedDialog<CrossScreenInfo> {
    private final TextField textField;
    private final TextField rowTextField;
    private final TextField columnTextField;
    private final int minCount;

    public NameDialog(int minCount, String defaultValue) {
      this.minCount = minCount;
      textField = new TextField(defaultValue);
      textField.setMaxWidth(Double.MAX_VALUE);
      textField.setId("cross-screen-name");
      GridPane.setHgrow(textField, Priority.ALWAYS);
      GridPane.setFillWidth(textField, true);

      // -- label
      Label label = DialogPaneEx.createContentLabel(getDialogPane().getContentText());
      label.setPrefWidth(Region.USE_COMPUTED_SIZE);
      label.textProperty().bind(getDialogPane().contentTextProperty());

      Label rowLabel =
          DialogPaneEx.createContentLabel(CaesarI18nCommonResource.getString("cross_screen.row"));
      rowLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);

      Label columnLabel =
          DialogPaneEx.createContentLabel(
              CaesarI18nCommonResource.getString("cross_screen.column"));
      columnLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);

      rowTextField = new TextField();
      rowTextField.setMaxWidth(Double.MAX_VALUE);
      GridPane.setHgrow(rowTextField, Priority.ALWAYS);
      GridPane.setFillWidth(rowTextField, true);
      UnaryOperator<Change> integerFilter =
          change -> {
            String newText = change.getControlNewText();
            if (newText.matches("[0-9]*")) {
              return change;
            }
            return null;
          };
      rowTextField.setTextFormatter(new TextFormatter<>(integerFilter));
      rowTextField.setId("cross-screen-row");

      columnTextField = new TextField();
      columnTextField.setMaxWidth(Double.MAX_VALUE);
      GridPane.setHgrow(columnTextField, Priority.ALWAYS);
      GridPane.setFillWidth(columnTextField, true);
      columnTextField.setTextFormatter(new TextFormatter<>(integerFilter));
      columnTextField.setId("cross-screen-column");

      GridPane grid = new GridPane();
      grid.setHgap(10);
      grid.setMaxWidth(Double.MAX_VALUE);
      grid.setAlignment(Pos.CENTER_LEFT);

      grid.add(label, 0, 0);
      grid.add(textField, 1, 0);

      grid.add(rowLabel, 0, 1);
      grid.add(rowTextField, 1, 1);

      grid.add(columnLabel, 0, 2);
      grid.add(columnTextField, 1, 2);

      setTitle(ControlResources.getString("Dialog.confirm.title"));
      getDialogPane().getStyleClass().add("text-input-dialog");
      getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
      getDialogPane().setContent(grid);
      setGraphic(null);
      setHeaderText(null);

      getDialogPane()
          .lookupButton(ButtonType.OK)
          .disableProperty()
          .bind(
              Bindings.createBooleanBinding(
                  () -> {
                    int row;
                    int column;
                    try {
                      row = Integer.parseInt(rowTextField.getText());
                      column = Integer.parseInt(columnTextField.getText());
                    } catch (NumberFormatException exception) {
                      return true;
                    }

                    return textField.getText().length() <= 0
                        || row * column < this.minCount
                        || row * column > 16;
                  },
                  textField.textProperty(),
                  rowTextField.textProperty(),
                  columnTextField.textProperty()));

      setResultConverter(
          (type) -> {
            if (type == ButtonType.OK) {
              CrossScreenInfo info = new CrossScreenInfo();
              info.setName(textField.getText());
              info.setRow(Integer.parseInt(rowTextField.getText()));
              info.setColumn(Integer.parseInt(columnTextField.getText()));
              return info;
            } else {
              return null;
            }
          });
    }
  }
}
