package com.mc.tool.caesar.vpm.pages.extenderupdate;

import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.VersionDef;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.ArrayIterable;
import com.mc.tool.caesar.api.utils.ExtenderUpdateInfo;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.update.UpdateData;
import com.mc.tool.caesar.vpm.pages.update.UpdateLogger;
import com.mc.tool.caesar.vpm.pages.update.UpdateTask;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutionException;
import javafx.application.Platform;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeTableView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.cpio.CpioArchiveEntry;
import org.apache.commons.compress.archivers.cpio.CpioArchiveInputStream;

/**
 * UpdateExtenderUpdater.
 */
@Slf4j
public class UpdateExtenderUpdater {
  private String filePath = null;
  private TreeTableView<UpdateData> tableView;
  private ExtenderUpdatePageController extenderPanel;

  CaesarDeviceController deviceController;
  List<TreeItem<UpdateData>> updateGroup = new ArrayList<>();
  List<TreeItem<UpdateData>> updateExtGroup = new ArrayList<>();
  List<TreeItem<UpdateData>> availableGroup = new ArrayList<>();
  List<UpdateTask> updateTasks = new ArrayList<>();
  boolean updateCancelled = false;
  private UpdateLogger logger;

  private static final int COMMON_EXT_UPDATE_PACK_SIZE = 512;
  private static final int VP6_EXT_UPDATE_PACK_SIZE = 100;

  /** . */
  public UpdateExtenderUpdater(
      CaesarDeviceController deviceController,
      UpdateLogger logger,
      ExtenderUpdatePageController extenderPanel) {
    this.logger = logger;
    this.extenderPanel = extenderPanel;
    this.deviceController = deviceController;
    this.tableView = extenderPanel.getTreeView();
  }

  public void setFilePath(String path) {
    filePath = path;
  }

  public void setAvailableGroup(List<TreeItem<UpdateData>> group) {
    this.availableGroup = group;
  }

  public void setTreeTableView(TreeTableView<UpdateData> tableView) {
    this.tableView = tableView;
  }

  /** . */
  public void updateCancel() {
    for (UpdateTask updateTask : updateTasks) {
      updateTask.setCancelled(true);
      updateTask.setFinished(true);
    }
    updateCancelled = true;
  }

  /** . */
  public void update() {
    updateExtGroup.clear();
    updateGroup.clear();
    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
      for (TreeItem<UpdateData> slotItem : dkmItem.getChildren()) {
        boolean canUpdate = false;
        for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
          if (typeItem.getValue().getSelected()) {
            canUpdate = true;
            if (!updateGroup.contains(typeItem)) {
              updateGroup.add(typeItem);
            }
          }
        }
        if (canUpdate && !updateExtGroup.contains(slotItem)) {
          updateExtGroup.add(slotItem);
        }
      }
    }
    if (updateExtGroup.isEmpty()) {
      return;
    }
    extenderPanel.updateStart();
    Thread thread = new Thread(() -> updateImpl(updateExtGroup));
    thread.setDaemon(true);
    thread.start();
  }

  /** . */
  public void updateImpl(List<TreeItem<UpdateData>> updateExtGroup) {
    CaesarSwitchDataModel model = deviceController.getDataModel();
    if (filePath.isEmpty()) {
      logger.addLog(String
          .format("[%s] update file: %s is empty!", deviceController.getLoginUser(), filePath));
      return;
    }
    ArrayList<ExtenderData> extenderDatas = new ArrayList<>();
    for (TreeItem<UpdateData> extItem : updateExtGroup) {
      ExtenderData extenderData =
          model.getConfigDataManager().getExtenderDataById(extItem.getValue().getId());
      if (null != extenderData) {
        extenderDatas.add(extenderData);
      }
    }
    updateCancelled = false;
    Collection<ExtenderUpdateInfo> extenderUpdateInfos =
        Utilities.getExtenderUpdateInfos(model, extenderDatas.toArray(new ExtenderData[0]));
    for (ExtenderUpdateInfo extenderUpdateInfo : extenderUpdateInfos) {
      if (null == extenderUpdateInfo) {
        continue;
      }
      String matAddress = extenderUpdateInfo.getAddress();
      UpdateExtenderTask updateExtenderTask = new UpdateExtenderTask(extenderUpdateInfo);
      updateTasks.add(updateExtenderTask);

      Platform.runLater(
          () -> {
            for (TreeItem<UpdateData> slotItem : updateExtGroup) {
              for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
                if (typeItem.getValue().getSelected()
                    && typeItem.getValue().getAddress().equals(matAddress)) {
                  log.info(
                      matAddress
                          + " "
                          + slotItem.getValue().getName()
                          + " "
                          + typeItem.getValue().getType()
                          + " bind");
                  typeItem
                      .getValue()
                      .getProgressBar()
                      .progressProperty()
                      .bind(updateExtenderTask.progressProperty());
                }
              }
            }
          });
      new Thread(updateExtenderTask).start();
    }
    boolean allFinished = false;
    while (!allFinished) {
      for (UpdateTask updateTask : updateTasks) {
        if (updateTask.isFinished()) {
          allFinished = true;
        } else {
          allFinished = false;
          break;
        }
      }
      if (!allFinished) {
        try {
          Thread.sleep(1000);
        } catch (InterruptedException exp) {
          logger.addLog("Sleep is interrupted!");
        }
      }
    }
    if (!updateCancelled) {
      Platform.runLater(
          () -> {
            // 记录结束日志及解绑进度条
            boolean allOk = true;
            for (TreeItem<UpdateData> slotItem : updateExtGroup) {
              for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
                if (typeItem.getValue().getSelected()) {
                  typeItem.getValue().getProgressBar().progressProperty().unbind();
                  if (checkUpdate(slotItem.getValue(), typeItem.getValue())) {
                    logger.addLog(
                        String.format("[%s] %s %s %s update success.",
                            deviceController.getLoginUser(),
                            slotItem.getParent().getValue().getName(),
                            slotItem.getValue().getName(),
                            typeItem.getValue().getType()));
                  } else {
                    logger.addLog(
                        String.format("[%s] %s %s %s update fail.",
                            deviceController.getLoginUser(),
                            slotItem.getParent().getValue().getName(),
                            slotItem.getValue().getName(),
                            typeItem.getValue().getType()));
                    allOk = false;
                  }
                  typeItem.getValue().getProgressBar().setProgress(0);
                  typeItem.getValue().setUpdateDate("");
                  typeItem.getValue().setUpdateVersion(null);
                }
              }
            }
            updateExtGroup.clear();

            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
            if (tableView != null && tableView.getScene() != null) {
              alert.initOwner(tableView.getScene().getWindow());
            }
            alert.setTitle(
                Bundle.NbBundle.getMessage(
                    CaesarExtenderUpdatePage.class, "ExtenderUpdate.update"));
            alert.setHeaderText(null);
            if (allOk) {
              alert.setContentText(
                  Bundle.NbBundle.getMessage(
                      CaesarExtenderUpdatePage.class, "ExtenderUpdate.update_all_ok"));
            } else {
              alert.setContentText(
                  Bundle.NbBundle.getMessage(
                      CaesarExtenderUpdatePage.class, "ExtenderUpdate.update_some_fail"));
            }
            alert.showAndWait();
          });
      try {
        deviceController
            .submit(
                () -> {
                  try {
                    deviceController.getDataModel().getSwitchModuleData().requestPorts();
                  } catch (ConfigException | BusyException exception) {
                    log.warn("Fail to update ports!", exception);
                  }
                })
            .get();
      } catch (ExecutionException | InterruptedException exception) {
        log.warn("Fail to request ports.", exception);
      }
      extenderPanel.updateNodes();
    } else {
      extenderPanel.setCancelled(false);
      for (TreeItem<UpdateData> typeItem : availableGroup) {
        typeItem.getValue().setDisabled(false);
      }
      logger.addLog(String.format("[%s] update cancelled!", deviceController.getLoginUser()));
      updateExtGroup.clear();
    }
    extenderPanel.setCancelled(false);
    extenderPanel.setUpdating(false);

    updateTasks.clear();
  }

  protected boolean checkUpdate(UpdateData slotItem, UpdateData typeItem) {
    ExtenderData extenderData = null;
    try {
      extenderData =
          deviceController
              .getDataModel()
              .getConfigDataManager()
              .getExtenderDataById(slotItem.getId());
    } catch (RuntimeException exception) {
      log.warn("", exception);
    }
    if (extenderData == null) {
      log.warn("Fail to find extender({})", slotItem.getId());
      return false;
    }

    boolean result = true;
    if (typeItem.getSelected() && typeItem.getUpdateVersion() != null) {
      VersionDef versionDef = typeItem.getUpdateVersion().copy();
      versionDef.setYear(versionDef.getYear() - 2000);
      switch (typeItem.getType()) {
        case UpdateConstant.UPDATE_APP_TYPE:
          result = extenderData.getVersion().getAppVersion().equals(versionDef);
          break;
        case UpdateConstant.UPDATE_FPGA_TYPE:
          result = extenderData.getVersion().getFpgaVersion().equals(versionDef);
          break;
        case UpdateConstant.UPDATE_SYS_TYPE:
          result = extenderData.getVersion().getSystemVersion().equals(versionDef);
          break;
        default:
          break;
      }
    }
    return result;
  }

  private class UpdateExtenderTask extends UpdateTask {
    ExtenderUpdateInfo extenderUpdateInfo;
    CaesarSwitchDataModel model;
    String address;

    public UpdateExtenderTask(ExtenderUpdateInfo extenderUpdateInfo) {
      super();
      this.extenderUpdateInfo = extenderUpdateInfo;
      this.model = extenderUpdateInfo.getModel();
      address = extenderUpdateInfo.getAddress();
      cancelled = false;
      finished = false;
    }

    @Override
    protected Object call() {
      try {
        int byteHasRead = 0;
        int threshold = 4;

        boolean isVpExt = false;
        int packageSize = COMMON_EXT_UPDATE_PACK_SIZE;
        // 记录开始日志及绑定进度条
        for (TreeItem<UpdateData> slotItem : updateExtGroup) {
          if (slotItem.getValue().getType().equals(UpdateConstant.EXTENDER_VPCON_TYPE)) {
            isVpExt = true;
          }
          for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
            if (typeItem.getValue().getSelected()
                && typeItem.getValue().getAddress().equals(address)) {
              String msg =
                  String.format(
                      "[%s] %s %s %s update start.", deviceController.getLoginUser(),
                      slotItem.getParent().getValue().getName(),
                      slotItem.getValue().getName(),
                      typeItem.getValue().getType());
              logger.addLog(msg);
            }
          }
        }
        // 开始升级
        log.info("[{}] Start to update {}.", deviceController.getLoginUser(), address);
        model.getController().setExtenderParallelUpdateOpen(extenderUpdateInfo.getUpdateParams());

        extenderPanel.setUpdating(true);
        extenderPanel.setCancelled(false);
        finished = false;
        File file = new File(filePath);
        long dataSize = file.length();
        updateProgress(0, dataSize);
        CpioArchiveInputStream cpioIn = null;
        boolean successful = false;
        try (FileInputStream fis = new FileInputStream(file)) {
          InputStream inputStream = fis;
          if (isVpExt) {
            ByteArrayInputStream bais = null;
            try {
              cpioIn = new CpioArchiveInputStream(fis);
              CpioArchiveEntry cpioEntry;
              while ((cpioEntry = (CpioArchiveEntry) cpioIn.getNextEntry()) != null) {
                if ("EXT_VP6.puw".equals(cpioEntry.getName())) {
                  ByteArrayOutputStream baos = new ByteArrayOutputStream();
                  int count;
                  byte[] data = new byte[512];
                  while ((count = cpioIn.read(data, 0, 512)) != -1) {
                    baos.write(data, 0, count);
                  }
                  byte[] allData = baos.toByteArray();
                  dataSize = allData.length;
                  bais = new ByteArrayInputStream(allData);
                  baos.close();
                  break;
                }
              }
            } finally {
              if (cpioIn != null) {
                cpioIn.close();
              }
            }
            if (bais == null) {
              logger.addLog(
                  String.format("[%s] Fail to find EXT_VP6.puw", deviceController.getLoginUser()));
              return null;
            } else {
              inputStream = bais;
            }
            packageSize = VP6_EXT_UPDATE_PACK_SIZE;
          }
          byte[] tmp = new byte[packageSize];
          int packageCount = (int) (dataSize / packageSize);
          log.debug("Extender update package count : {}.", packageCount);
          int readOnce;
          while ((readOnce = inputStream.read(tmp)) != -1) {
            int time = 0;
            if (!cancelled) {
              if (readOnce < packageSize) {
                byte[] lastTmp = new byte[readOnce];
                System.arraycopy(tmp, 0, lastTmp, 0, readOnce);
                while (time < threshold) {
                  try {
                    model
                        .getController()
                        .setExtenderParallelUpdateWrite(byteHasRead, lastTmp, true);
                    break;
                  } catch (Exception ex) {
                    time++;
                    logger.addLog(
                        String.format("[%s] Send timeout", deviceController.getLoginUser()),
                        ex);
                    if (time == threshold) {
                      throw ex;
                    }
                  }
                }
              } else {
                while (time < threshold) {
                  try {
                    model.getController().setExtenderParallelUpdateWrite(byteHasRead, tmp, true);
                    break;
                  } catch (Exception ex) {
                    time++;
                    logger.addLog(String.format("[%s] Send timeout", deviceController.getLoginUser()), ex);
                    if (time == threshold) {
                      throw ex;
                    }
                  }
                }
              }
              byteHasRead += readOnce;
              updateProgress(byteHasRead, dataSize);
            } else {
              break;
            }
          }
          if (!cancelled) {
            successful = true;
          }
        } finally {
          model.getController().setExtenderParallelUpdateClose(successful);
        }
        if (!cancelled) {
          // 等待版本更新
          readNewVersion();
        }
      } catch (DeviceConnectionException ex) {
        logger.addLog(String.format("[%s] DeviceConnect fail", deviceController.getLoginUser()), ex);
        updateCancel();
      } catch (ConfigException ex) {
        logger.addLog(String.format("[%s] Config fail", deviceController.getLoginUser()), ex);
        updateCancel();

      } catch (BusyException ex) {
        logger.addLog(String.format("[%s] Busy", deviceController.getLoginUser()), ex);
        updateCancel();
      } catch (FileNotFoundException ex) {
        logger.addLog(String.format("[%s] File not found", deviceController.getLoginUser()), ex);
        updateCancel();
      } catch (IOException ex) {
        logger.addLog(String.format("[%s] IO fail", deviceController.getLoginUser()), ex);
        updateCancel();
      } finally {
        finished = true;
      }
      return null;
    }

    @Override
    protected void updateOpen() {
      // TODO Auto-generated method stub

    }

    @Override
    protected void updateWrite(int offset, byte[] dataToWrite) {
      // TODO Auto-generated method stub

    }

    @Override
    protected void updateClose(boolean successful) {
      // TODO Auto-generated method stub

    }

    protected void readNewVersion() {
      logger.addLog(String.format("[%s] Waiting for reboot!", deviceController.getLoginUser()));
      ExtenderData[] validExtenderData =
          updateExtGroup.stream()
              .map(
                  item ->
                      deviceController
                          .getDataModel()
                          .getConfigDataManager()
                          .getExtenderDataById(item.getValue().getId()))
              .toArray(ExtenderData[]::new);
      ArrayIterable<ExtenderData> extenderIterable = new ArrayIterable<>(validExtenderData);
      //外设升级等待时间，按外设数量计算，100个外设5分钟，200个外设10分钟
      int times = (int) Math.ceil((double) validExtenderData.length / 100);
      int count = 0;
      int maxCount = 150 * times;
      while (count < maxCount) {
        try {
          deviceController
              .submit(
                  () -> {
                    try {
                      deviceController.getDataModel().reloadExtenderVersion(false);
                      deviceController.getDataModel().reloadExtenderData(extenderIterable);
                    } catch (ConfigException | BusyException exception) {
                      log.warn("Fail to reload extender data!", exception);
                    }
                  })
              .get();
        } catch (ExecutionException | InterruptedException exception) {
          log.warn("Fail to load extenders!", exception);
        }

        boolean pass = true;
        for (TreeItem<UpdateData> slotItem : updateExtGroup) {
          for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
            pass = checkUpdate(slotItem.getValue(), typeItem.getValue());
            if (!pass) {
              break;
            }
          }
          if (!pass) {
            break;
          }
        }

        if (pass) {
          break;
        } else {
          try {
            Thread.sleep(2000);
          } catch (InterruptedException exception) {
            log.warn("Sleep is intterupted!", exception);
          }
          count++;
        }
      }
      if (count >= maxCount) {
        logger.addLog(String.format("[%s] Fail to update some extenders!", deviceController.getLoginUser()));
      }
    }
  }
}
