package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.menu.MenuGroup;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.menu.predicate.TypePredicate;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;
import javafx.scene.control.MenuItem;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
@SuppressFBWarnings("BC_UNCONFIRMED_CAST_OF_RETURN_VALUE")
public class MenuMultiScreen4Caesar extends MenuItem {
  protected SystemEditControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuMultiScreen4Caesar(SystemEditControllable controllable) {
    this.controllable = controllable;
    this.setText(Bundle.NbBundle.getMessage("menu.scenariogroup"));
    this.setOnAction(event -> onAction());

    this.disableProperty().bind(getMenuDisableBinding(controllable));
  }

  /**
   * 获取菜单是否应该禁掉的binding.
   *
   * @param controllable controllable
   * @return binding
   */
  public static MenuPredicateBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = MenuGroup.getMenuDisableBinding(controllable);
    Predicate<VisualEditNode> notTypePredicate =
        new TypePredicate(VisualEditTerminal.class).negate();
    binding.addSingleSelectionPredicate(notTypePredicate);
    binding.addSingleSelectionPredicate(VisualEditNode::isTx);
    binding.addSingleSelectionPredicate(node -> !(node instanceof CaesarConTerminal));
    binding.addSingleSelectionPredicate(
        node -> node instanceof CaesarConTerminal && ((CaesarConTerminal) node).isVp());
    binding.addSingleSelectionPredicate(node -> node instanceof CaesarConTerminal
        && ((CaesarConTerminal) node).getExtenderData().getExtenderStatusInfo().getSpecialExtType()
            == CaesarConstants.Extender.SpecialExtenderType.HW_PREVIEW);
    return binding;
  }

  protected void onAction() {
    Collection<CellSkin> skins = controllable.getGraph().getSelectionModel().getSelectedCellSkin();
    List<VisualEditNode> nodeList = new ArrayList<>();
    for (CellSkin skin : skins) {
      if (skin.getCell().getBindedObject() instanceof VisualEditNode) {
        nodeList.add((VisualEditNode) skin.getCell().getBindedObject());
      } else {
        log.warn(
            "Cell's bindedobject is not VisualEditNode but {}", skin.getCell().getBindedObject());
      }
    }
    CaesarMultiScreenFunc func =
        controllable.addGroup(
            "Group", CaesarMultiScreenFunc.class, nodeList.toArray(new VisualEditNode[0]));
    if (func != null) {
      if (nodeList.size() < CaesarMultiScreenFunc.DEFAULT_COLUMN_SIZE) {
        func.setLayout(1, nodeList.size());
      } else {
        func.setLayout(
            (int) Math.ceil(nodeList.size() / (double) CaesarMultiScreenFunc.DEFAULT_COLUMN_SIZE),
            CaesarMultiScreenFunc.DEFAULT_COLUMN_SIZE);
      }
    }
  }
}
