package com.mc.tool.caesar.vpm.pages.operation.videowall.vp;

import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarOsdData;
import com.mc.tool.caesar.vpm.util.vp.Vp6Constants;
import com.mc.tool.caesar.vpm.util.vp.VpOsdData;
import javafx.scene.paint.Color;

/**
 * .
 */
public class CaesarVpOsdData implements VpOsdData {

  private CaesarOsdData osdData;

  public CaesarVpOsdData(CaesarOsdData osdData) {
    this.osdData = osdData;
  }

  @Override
  public int getOsdLeft() {
    return osdData.getOsdLeft();
  }

  @Override
  public int getOsdTop() {
    return osdData.getOsdTop();
  }

  @Override
  public int getOsdWidth() {
    return osdData.getOsdWidth();
  }

  @Override
  public int getOsdHeight() {
    return osdData.getOsdHeight();
  }

  @Override
  public int getOsdAlpha() {
    return (int) (osdData.getOsdAlpha() * Vp6Constants.ALPHA_FACTOR);
  }

  @Override
  public Color getOsdColor() {
    return osdData.getOsdColor();
  }

  @Override
  public Color getBgColor() {
    return osdData.getBgColor();
  }

  @Override
  public boolean isShowLogo() {
    return osdData.isShowLogo();
  }

  @Override
  public boolean isEnableBgImg() {
    return osdData.isEnableBgImg();
  }

  @Override
  public boolean isEnableBanner() {
    return osdData.isEnableBanner();
  }

  @Override
  public boolean isEnableBannerBg() {
    return osdData.isEnableBannerBg();
  }

  @Override
  public int getBgImgWidth() {
    return osdData.getBgImgWidth();
  }

  @Override
  public int getBgImgHeight() {
    return osdData.getBgImgHeight();
  }

  @Override
  public boolean isEnableRedundant() {
    return osdData.isEnableRedundant();
  }
}
