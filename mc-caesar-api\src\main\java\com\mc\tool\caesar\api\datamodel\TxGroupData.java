package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.Nameable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import lombok.Getter;

/**
 * GroupData的配置数据.
 */
public final class TxGroupData extends AbstractData implements CaesarCommunicatable, Nameable {

  public static final String PROPERTY_STATUS = "TxGroupData.Status";
  public static final String PROPERTY_NAME = "TxGroupData.Name";

  @Expose
  @Getter
  private String name;
  @Expose
  @Getter
  private int status;


  /**
   * Constructor.
   */
  public TxGroupData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
      String fqn) {
    super(pcs, configDataManager, oid, fqn);
    initCommitRollback();
  }

  /**
   * 设置属性.
   *
   * @param property 属性名称.
   * @param value    属性值
   */
  public void setProperty(String property, Object value) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (property.equals(PROPERTY_NAME) && value instanceof String) {
      setName((String) value);
    } else if (property.equals(PROPERTY_STATUS) && value instanceof Integer) {
      setStatus((Integer) value);
    }
  }

  /**
   * setName.
   */
  public void setName(String name) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldName = this.name;
    this.name = name;
    firePropertyChange(PROPERTY_NAME, oldName, name);
  }


  @Override
  public void initDefaults() {
    super.initDefaults();
    setName("");
    setStatus(0);
  }

  public void delete() {
    setName("");
    setStatus(0);
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(PROPERTY_STATUS, oldValue, this.status);
  }

  public void setStatusActive(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.TxGroup.Status.ACTIVE));
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.TxGroup.Status.ACTIVE);
  }

  @Override
  public void readData(CfgReader paramCfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<TxGroupData> converter =
        getConfigDataManager().getConfigMetaData().getUtilVersion()
            .getDataConverter(TxGroupData.class);
    converter.readData(this, paramCfgReader);
  }

  @Override
  public void writeData(CfgWriter paramCfgWriter) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<TxGroupData> converter =
        getConfigDataManager().getConfigMetaData().getUtilVersion()
            .getDataConverter(TxGroupData.class);
    converter.writeData(this, paramCfgWriter);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (propertyName.equals(PROPERTY_NAME) && value instanceof String) {
      setName((String) value);
    } else if (propertyName.equals(PROPERTY_STATUS) && value instanceof Integer) {
      setStatus((Integer) value);
    }
  }

  @Override
  public String toString() {
    return String.format("%s[%d]", name, getOid());
  }
}
