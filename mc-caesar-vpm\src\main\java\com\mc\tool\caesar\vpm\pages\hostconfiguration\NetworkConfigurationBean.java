package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import com.mc.common.validation.constraints.StringFormat;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.utils.IpUtil;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * .
 */
public class NetworkConfigurationBean {

  /**
   * 排除前后为0或255的ip.
   * <a href="https://c.runoob.com/front-end/7625">可视化预览表达式</a>
   */
  private static final String IP_REGEX = "((2(5[0-4]|[0-4]\\d))|1\\d{1,2}|[1-9]\\d?)"
      + "(\\.((2(5[0-5]|[0-4]\\d))|1\\d{1,2}|[1-9]\\d?|0)){2}"
      + "(\\.((2(5[0-4]|[0-4]\\d))|1\\d{1,2}|[1-9]\\d?))";
  private final SystemConfigData systemConfigData;

  private String ipAddress1;
  public static final String IP_ADDRESS_1 = "ipAddress1";
  private String netMask1;
  public static final String NETMASK_1 = "netMask1";
  private String gateway1;
  public static final String GATEWAY_1 = "gateway1";
  private String macAddress1;
  public static final String MAC_ADDRESS_1 = "macAddress1";

  private String ipAddress2;
  public static final String IP_ADDRESS_2 = "ipAddress2";
  private String netMask2;
  public static final String NETMASK_2 = "netMask2";
  private String gateway2;
  public static final String GATEWAY_2 = "gateway2";
  private String dualMasterVirtualIp;
  public static final String DUAL_MASTER_VIRTUAL_IP = "dualMasterVirtualIp";
  private int dualMasterVirtualRouteId;
  public static final String DUAL_MASTER_VIRTUAL_ROUTE_ID = "dualMasterVirtualRouteId";

  public NetworkConfigurationBean(SystemConfigData systemConfigData) {
    this.systemConfigData = systemConfigData;
  }

  /** getIpAddress. */
  @StringFormat(format = IP_REGEX)
  public String getIpAddress1() {
    ipAddress1 =
        systemConfigData.getNetworkDataPreset1().isDhcp()
            ? IpUtil.getAddressString(systemConfigData.getNetworkDataCurrent1().getAddress())
            : IpUtil.getAddressString(systemConfigData.getNetworkDataPreset1().getAddress());
    return ipAddress1;
  }

  /** setIpAddress. */
  public void setIpAddress1(String ip) {
    if (systemConfigData.getNetworkDataPreset1().isDhcp()) {
      systemConfigData.getNetworkDataCurrent1().setAddress(IpUtil.getAddressByte(ip));
    } else {
      systemConfigData.getNetworkDataPreset1().setAddress(IpUtil.getAddressByte(ip));
    }
  }

  /** getNetMask. */
  @StringFormat(
      format =
          "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})" + "(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}")
  public String getNetMask1() {
    netMask1 =
        systemConfigData.getNetworkDataPreset1().isDhcp()
            ? IpUtil.getAddressString(systemConfigData.getNetworkDataCurrent1().getNetmask())
            : IpUtil.getAddressString(systemConfigData.getNetworkDataPreset1().getNetmask());
    return netMask1;
  }

  /** setNetMask. */
  public void setNetMask1(String ip) {
    if (systemConfigData.getNetworkDataPreset1().isDhcp()) {
      systemConfigData.getNetworkDataCurrent1().setNetmask(IpUtil.getAddressByte(ip));
    } else {
      systemConfigData.getNetworkDataPreset1().setNetmask(IpUtil.getAddressByte(ip));
    }
  }

  /** getGateway. */
  @StringFormat(
      format =
          "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})" + "(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}")
  public String getGateway1() {
    gateway1 =
        systemConfigData.getNetworkDataPreset1().isDhcp()
            ? IpUtil.getAddressString(systemConfigData.getNetworkDataCurrent1().getGateway())
            : IpUtil.getAddressString(systemConfigData.getNetworkDataPreset1().getGateway());
    return gateway1;
  }

  /** setGateway. */
  public void setGateway1(String ip) {
    if (systemConfigData.getNetworkDataPreset1().isDhcp()) {
      systemConfigData.getNetworkDataCurrent1().setGateway(IpUtil.getAddressByte(ip));
    } else {
      systemConfigData.getNetworkDataPreset1().setGateway(IpUtil.getAddressByte(ip));
    }
  }

  /** getMacAddress. */
  public String getMacAddress1() {
    macAddress1 =
        systemConfigData.getNetworkDataCurrent1().getMacAddress() == null
            ? ""
            : systemConfigData.getNetworkDataCurrent1().getMacAddress();
    return macAddress1;
  }

  /** . */
  public void setMacAddress1(String ip) {
    // systemConfigData.getNetworkDataCurrent1().setMacAddress(ip);
  }

  /** getIpAddress2. */
  @StringFormat(format = IP_REGEX)
  public String getIpAddress2() {
    ipAddress2 =
        systemConfigData.getNetworkDataPreset3().isDhcp()
            ? IpUtil.getAddressString(systemConfigData.getNetworkDataCurrent3().getAddress())
            : IpUtil.getAddressString(systemConfigData.getNetworkDataPreset3().getAddress());
    return ipAddress2;
  }

  /** setIpAddress2. */
  public void setIpAddress2(String ip) {
    if (systemConfigData.getNetworkDataPreset3().isDhcp()) {
      systemConfigData.getNetworkDataCurrent3().setAddress(IpUtil.getAddressByte(ip));
    } else {
      systemConfigData.getNetworkDataPreset3().setAddress(IpUtil.getAddressByte(ip));
    }
  }

  /** getNetMask. */
  @StringFormat(
      format =
          "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})" + "(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}")
  public String getNetMask2() {
    netMask2 =
        systemConfigData.getNetworkDataPreset3().isDhcp()
            ? IpUtil.getAddressString(systemConfigData.getNetworkDataCurrent3().getNetmask())
            : IpUtil.getAddressString(systemConfigData.getNetworkDataPreset3().getNetmask());
    return netMask2;
  }

  /** setNetMask. */
  public void setNetMask2(String ip) {
    if (systemConfigData.getNetworkDataPreset3().isDhcp()) {
      systemConfigData.getNetworkDataCurrent3().setNetmask(IpUtil.getAddressByte(ip));
    } else {
      systemConfigData.getNetworkDataPreset3().setNetmask(IpUtil.getAddressByte(ip));
    }
  }

  /** getGateway. */
  @StringFormat(
      format =
          "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})" + "(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}")
  public String getGateway2() {
    gateway2 =
        systemConfigData.getNetworkDataPreset3().isDhcp()
            ? IpUtil.getAddressString(systemConfigData.getNetworkDataCurrent3().getGateway())
            : IpUtil.getAddressString(systemConfigData.getNetworkDataPreset3().getGateway());
    return gateway2;
  }

  /** setGateway. */
  public void setGateway2(String ip) {
    if (systemConfigData.getNetworkDataPreset3().isDhcp()) {
      systemConfigData.getNetworkDataCurrent3().setGateway(IpUtil.getAddressByte(ip));
    } else {
      systemConfigData.getNetworkDataPreset3().setGateway(IpUtil.getAddressByte(ip));
    }
  }

  @StringFormat(format = "[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}")
  public String getDualMasterVirtualIp() {
    dualMasterVirtualIp = IpUtil.getAddressString(systemConfigData.getNetworkDataPreset4().getAddress());
    return dualMasterVirtualIp;
  }

  /**
   * setDualMasterVirtualIp.
   */
  public void setDualMasterVirtualIp(String ip) {
    systemConfigData.getNetworkDataPreset4().setAddress(IpUtil.getAddressByte(ip));
  }

  @Max(255)
  @Min(1)
  public int getDualMasterVirtualRouteId() {
    dualMasterVirtualRouteId = systemConfigData.getNetworkDataPreset4().getNetworkBits();
    return dualMasterVirtualRouteId;
  }

  /**
   * setDualMasterVirtualRouteId.
   */
  public void setDualMasterVirtualRouteId(int dualMasterVirtualRouteId) {
    if (systemConfigData.getNetworkDataPreset2().getNetworkBits() != dualMasterVirtualRouteId) {
      systemConfigData.getNetworkDataPreset4().setNetworkBits(dualMasterVirtualRouteId);
    }
  }
}
