package com.mc.tool.caesar.api.datamodel.vp;

import javafx.scene.paint.Color;

/**
 * .
 */
public interface OsdInfoSetter {

  void setOsdLeft(int value);

  void setOsdTop(int value);

  void setOsdWidth(int value);

  void setOsdHeight(int value);

  void setOsdAlpha(double value);

  void setOsdColor(Color value);

  void setBgColor(Color value);

  void setShowLogo(boolean show);

  void setEnableBgImg(boolean enable);

  void setBgImgWidth(int value);

  void setBgImgHeight(int value);

  void setDisableSyncData(boolean disable);

  void setEnableRedundant(boolean enable);

  void setEnableBanner(boolean enable);

  void setEnableBannerBg(boolean enable);
}
