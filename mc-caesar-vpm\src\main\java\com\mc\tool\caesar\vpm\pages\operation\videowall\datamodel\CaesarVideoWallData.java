package com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.datamodel.vp.LayoutInfoGetter;
import com.mc.tool.caesar.api.datamodel.vp.LayoutInfoSetter;
import com.mc.tool.caesar.api.datamodel.vp.LayoutSize;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.framework.operation.videowall.datamodel.IVideoWallLayout;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.operation.videowall.datamodel.LogicLayoutData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.util.Pair;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class CaesarVideoWallData implements VideoWallObject, LayoutInfoGetter, LayoutInfoSetter {
  @Getter private BooleanProperty selected = new SimpleBooleanProperty(false);
  @Expose @Getter @Setter private int id = -1;

  @Expose @Getter private StringProperty name = new SimpleStringProperty("");

  @Expose @Getter @Setter private short type;

  @Expose @Getter private LayoutData layoutData = new LayoutData();

  @Expose @Getter private CaesarOutputData outputData = new CaesarOutputData();

  @Expose @Getter private CaesarOsdData osdData = new CaesarOsdData();

  @Expose @Getter
  private ObservableList<CaesarScreenData> screens = FXCollections.observableArrayList();

  @Expose @Getter
  private ObservableList<CaesarVideoData> videos = FXCollections.observableArrayList();

  @Expose @Getter private LogicLayoutData logicLayoutData = new LogicLayoutData();

  @Expose @Getter private BooleanProperty useLogicLayout = new SimpleBooleanProperty(true);

  @Expose @Getter private CaesarTestFrameData testFrameData = new CaesarTestFrameData();

  @Expose @Getter private IntegerProperty audioGroupIndex = new SimpleIntegerProperty(0);

  /** Constructor. */
  public CaesarVideoWallData() {}

  /** 初始化. */
  public void init() {
    layoutData
        .getColumnsProperty()
        .addListener((observable, oldValue, newValue) -> updateScreens());

    layoutData.getRowsProperty().addListener((observable, oldValue, newValue) -> updateScreens());

    logicLayoutData
        .getTotalWidthProperty()
        .bind(
            Bindings.createIntegerBinding(
                () -> layoutData.getTotalWidth(),
                layoutData.getColumnsProperty(),
                layoutData.getResWidth(),
                layoutData.getMultiRes(),
                layoutData.getMultiResObservable()));

    logicLayoutData
        .getTotalHeightProperty()
        .bind(
            Bindings.createIntegerBinding(
                () -> layoutData.getTotalHeight(),
                layoutData.getRowsProperty(),
                layoutData.getResHeight(),
                layoutData.getMultiRes(),
                layoutData.getMultiResObservable()));
  }

  @Override
  public IVideoWallLayout getUserLayout() {
    if (useLogicLayout.get()) {
      return logicLayoutData;
    } else {
      return layoutData;
    }
  }

  protected void updateScreens() {
    List<CaesarScreenData> newScreens = new ArrayList<>(screens);
    int screenCount = layoutData.getColumns() * getLayoutData().getRows();
    int oldCount = newScreens.size();
    if (screenCount > oldCount) {
      for (int i = 0; i < (screenCount - oldCount); i++) {
        newScreens.add(new CaesarScreenData());
      }
    } else {
      newScreens = newScreens.subList(0, screenCount);
    }
    screens.setAll(newScreens);
  }

  @Override
  public int getScreenCount() {
    return screens.size();
  }

  @Override
  public boolean addVideo(VideoObject video) {
    if (video instanceof CaesarVideoData) {
      videos.add((CaesarVideoData) video);
      return true;
    } else {
      return false;
    }
  }

  @Override
  public boolean addVideo(int index, VideoObject video) {
    if (video instanceof CaesarVideoData) {
      videos.add(index, (CaesarVideoData) video);
      return true;
    } else {
      return false;
    }
  }

  @Override
  public boolean addAll(int index, Collection<VideoObject> videos) {
    List<CaesarVideoData> newItems = new ArrayList<>();
    for (VideoObject item : videos) {
      if (!(item instanceof CaesarVideoData)) {
        return false;
      }
      newItems.add((CaesarVideoData) item);
    }
    this.videos.addAll(index, newItems);
    return true;
  }

  @Override
  public Pair<Integer, Integer> getScreenLocation(ScreenObject screenData) {
    int index = screens.indexOf(screenData);
    if (index < 0) {
      return new Pair<>(-1, -1);
    }
    return new Pair<>(index % layoutData.getColumns(), index / layoutData.getColumns());
  }

  @Override
  public int getScreenIndex(ScreenObject screenData) {
    return screens.indexOf(screenData);
  }

  @Override
  public void copyTo(VideoWallObject input, boolean copyScreen) {
    if (!(input instanceof CaesarVideoWallData)) {
      return;
    }
    CaesarVideoWallData copy = (CaesarVideoWallData) input;
    copy.name.set(name.get());
    copy.type = type;
    layoutData.copyTo(copy.layoutData);

    copy.osdData.osdLeft.set(osdData.osdLeft.get());
    copy.osdData.osdTop.set(osdData.osdTop.get());
    copy.osdData.osdWidth.set(osdData.osdWidth.get());
    copy.osdData.osdHeight.set(osdData.osdHeight.get());
    copy.osdData.osdAlpha.set(osdData.osdAlpha.get());
    copy.osdData.osdColor.set(osdData.osdColor.get());
    copy.osdData.bgColor.set(osdData.bgColor.get());
    copy.osdData.showLogo.set(osdData.showLogo.get());
    copy.osdData.enableBgImg.set(osdData.enableBgImg.get());
    copy.osdData.bgImgWidth.set(osdData.bgImgWidth.get());
    copy.osdData.bgImgHeight.set(osdData.bgImgHeight.get());
    copy.osdData.disableSyncData.set(osdData.disableSyncData.get());
    copy.osdData.enableRedundant.set(osdData.enableRedundant.get());

    copy.outputData.enable.set(outputData.enable.get());
    copy.outputData.horzSync.set(outputData.horzSync.get());
    copy.outputData.horzBackPorch.set(outputData.horzBackPorch.get());
    copy.outputData.horzFrontPorch.set(outputData.horzFrontPorch.get());
    copy.outputData.horzPolarity.set(outputData.horzPolarity.get());
    copy.outputData.vertSync.set(outputData.vertSync.get());
    copy.outputData.vertBackPorch.set(outputData.vertBackPorch.get());
    copy.outputData.vertFrontPorch.set(outputData.vertFrontPorch.get());
    copy.outputData.vertPolarity.set(outputData.vertPolarity.get());
    copy.outputData.clock.set(outputData.clock.get());

    copy.testFrameData.setMode(testFrameData.getMode());
    copy.testFrameData.setColor(testFrameData.getColor());
    copy.testFrameData.setSpeed(testFrameData.getSpeed());
    copy.testFrameData.setAlpha(testFrameData.getAlpha());

    copy.audioGroupIndex.set(audioGroupIndex.get());

    copy.videos.clear();
    for (VideoObject videoData : videos) {
      CaesarVideoData newVideo = new CaesarVideoData();
      videoData.copyTo(newVideo);
      copy.videos.add(newVideo);
    }

    if (copyScreen) {
      copy.screens.clear();
      for (ScreenObject screenData : screens) {
        CaesarScreenData newScreen = new CaesarScreenData();
        screenData.copyTo(newScreen);
        copy.screens.add(newScreen);
      }
    }
  }

  @Override
  public int getHorzCount() {
    return getLayoutData().getColumns();
  }

  @Override
  public int getVertCount() {
    return getLayoutData().getRows();
  }

  @Override
  public int getResolutionHeight() {
    return getLayoutData().getResHeight().get();
  }

  @Override
  public int getResolutionWidth() {
    return getLayoutData().getResWidth().get();
  }

  @Override
  public void setHorzCount(int value) {
    getLayoutData().getColumnsProperty().set(value);
  }

  @Override
  public void setVertCount(int value) {
    getLayoutData().getRowsProperty().set(value);
  }

  @Override
  public void setResolutionHeight(int value) {
    getLayoutData().getResHeight().set(value);
  }

  @Override
  public void setResolutionWidth(int value) {
    getLayoutData().getResWidth().set(value);
  }

  @Override
  public void setResolution(LayoutSize value) {
    getLayoutData().getResWidth().set(value.getHorzLength());
    getLayoutData().getResHeight().set(value.getVertLength());
  }

  /**
   * 创建设备用的output数据.
   *
   * @return output数据
   */
  public Vp6OutputData createVp6Output() {
    Vp6OutputData vp6OutputData = new Vp6OutputData();
    vp6OutputData.enable.set((short) (outputData.isEnable() ? 1 : 0));
    vp6OutputData.clock.set(outputData.getClock());
    vp6OutputData.vertOutput.activeVideoTime.set(layoutData.getResHeight().get());
    vp6OutputData.vertOutput.backPorch.set(outputData.getVertBackPorch());
    vp6OutputData.vertOutput.frontPorch.set(outputData.getVertFrontPorch());
    vp6OutputData.vertOutput.polarity.set((short) (outputData.isVertPolarity() ? 1 : 0));
    vp6OutputData.vertOutput.sync.set(outputData.getVertSync());

    vp6OutputData.horzOutput.activeVideoTime.set(layoutData.getResWidth().get());
    vp6OutputData.horzOutput.backPorch.set(outputData.getHorzBackPorch());
    vp6OutputData.horzOutput.frontPorch.set(outputData.getHorzFrontPorch());
    vp6OutputData.horzOutput.polarity.set((short) (outputData.isHorzPolarity() ? 1 : 0));
    vp6OutputData.horzOutput.sync.set(outputData.getHorzSync());
    return vp6OutputData;
  }

  @Override
  public boolean setVideo(int index, VideoObject video) {
    if (video instanceof CaesarVideoData) {
      videos.set(index, (CaesarVideoData) video);
      return true;
    } else {
      return false;
    }
  }

  @Override
  public boolean hasIndex() {
    return id >= 0;
  }

  @Override
  public int getIndex() {
    return id;
  }

  @Override
  public Collection<Integer> getResolutionWidths() {
    return new ArrayList<>(layoutData.getWidths());
  }

  @Override
  public Collection<Integer> getResolutionHeights() {
    return new ArrayList<>(layoutData.getHeights());
  }

  @Override
  public boolean isMultipleResolutionEnable() {
    return layoutData.getMultiRes().get();
  }

  @Override
  public Collection<Integer> getHorzMargins() {
    return new ArrayList<>(layoutData.getHorzMargins());
  }

  @Override
  public Collection<Integer> getVertMargins() {
    return new ArrayList<>(layoutData.getVertMargins());
  }

  @Override
  public void setEnableMultipleResolution(boolean enable) {
    layoutData.getMultiRes().set(enable);
  }

  @Override
  public void setResolutionWidths(Collection<Integer> widths) {
    layoutData.updateMultiResWidths(widths);
  }

  @Override
  public void setResolutionHeights(Collection<Integer> heights) {
    layoutData.updateMultiResHeights(heights);
  }

  @Override
  public void setHorzMargins(Collection<Integer> horzMargins) {
    layoutData.updateHorzMargins(horzMargins);
  }

  @Override
  public void setVertMargins(Collection<Integer> vertMargins) {
    layoutData.updateVertMargins(vertMargins);
  }

  @Override
  public int getCompensationScaleThreshold() {
    return layoutData.getCompensationScaleThreshold().get();
  }

  @Override
  public int getLeftCompensation() {
    return layoutData.getLeftCompensation().get();
  }

  @Override
  public int getRightCompensation() {
    return layoutData.getRightCompensation().get();
  }

  @Override
  public int getTopCompensation() {
    return layoutData.getTopCompensation().get();
  }

  @Override
  public int getBottomCompensation() {
    return layoutData.getBottomCompensation().get();
  }

  @Override
  public void setCompensationScaleThreshold(int value) {
    layoutData.getCompensationScaleThreshold().set(value);
  }

  @Override
  public void setLeftCompensation(int value) {
    layoutData.getLeftCompensation().set(value);
  }

  @Override
  public void setRightCompensation(int value) {
    layoutData.getRightCompensation().set(value);
  }

  @Override
  public void setTopCompensation(int value) {
    layoutData.getTopCompensation().set(value);
  }

  @Override
  public void setBottomCompensation(int value) {
    layoutData.getBottomCompensation().set(value);
  }

  public boolean isVp6() {
    return type == 0;
  }

  public boolean isVp7() {
    return type == 1;
  }
}
